[tool.poetry]
name = "stat359_su25"
version = "0.1.0"
description = ""
package-mode = false
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
gensim = "^4.3.3"
matplotlib = "^3.10.3"
scipy = "<1.14.0"
scikit-learn = "^1.7.0"
datasets = "^3.6.0"
ipywidgets = "^8.1.7"
nltk = "^3.9.1"
torch = "^2.7.1"
torchsummary = "^1.5.1"
seaborn = "^0.13.2"
sentence-transformers = "^4.1.0"
accelerate = ">=0.26.0"
ipykernel = "^6.29.5"
transformers = "^4.52.4"
tensorboard = "^2.19.0"
langchain = "^0.3.26"
langchain-ollama = "^0.3.3"


[tool.poetry.group.dev.dependencies]
ipykernel = "^6.29.5"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
