#!/usr/bin/env python3
"""
Quick test script to verify clustering visualizations work correctly.
"""

import pickle
import numpy as np
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import matplotlib.pyplot as plt
from gensim.models import KeyedVectors
import warnings
warnings.filterwarnings('ignore')

# Use non-interactive backend
import matplotlib
matplotlib.use('Agg')

print("🧪 Testing clustering visualizations...")

# Load embeddings
print("Loading embeddings...")
with open('test_word2vec_embeddings.pkl', 'rb') as f:
    pytorch_data = pickle.load(f)
pytorch_embeddings = pytorch_data['embeddings']
pytorch_word2idx = pytorch_data['word2idx']

with open('word2vec_gensim_embeddings.pkl', 'rb') as f:
    gensim_data = pickle.load(f)
gensim_embeddings = gensim_data['embeddings']
gensim_word2idx = gensim_data['word2idx']

try:
    google_model = KeyedVectors.load('word2vec-google-news-300.model')
    google_available = True
    print("✅ All embeddings loaded successfully!")
except:
    google_available = False
    print("⚠️ Google News model not available, testing with PyTorch and Gensim only")

# Test sample words
sample_words = ['cat', 'dog', 'mat', 'rug', 'sit', 'weather', 'mathematics', 'comfortable']

def get_word_embeddings_for_test(words, embedding_type):
    """Extract embeddings for test words."""
    embeddings = []
    valid_words = []
    
    for word in words:
        if embedding_type == 'pytorch' and word in pytorch_word2idx:
            idx = pytorch_word2idx[word]
            embeddings.append(pytorch_embeddings[idx])
            valid_words.append(word)
        elif embedding_type == 'gensim' and word in gensim_word2idx:
            idx = gensim_word2idx[word]
            embeddings.append(gensim_embeddings[idx])
            valid_words.append(word)
        elif embedding_type == 'google' and google_available and word in google_model.key_to_index:
            embeddings.append(google_model[word])
            valid_words.append(word)
    
    return np.array(embeddings), valid_words

# Test PCA
print("\n🔍 Testing PCA...")
pytorch_embs, pytorch_words = get_word_embeddings_for_test(sample_words, 'pytorch')
print(f"PyTorch: {len(pytorch_words)} words found: {pytorch_words}")

if len(pytorch_embs) >= 2:
    pca = PCA(n_components=2)
    pytorch_2d = pca.fit_transform(pytorch_embs)
    print(f"✅ PCA successful! Shape: {pytorch_2d.shape}")
    print(f"   Explained variance: {pca.explained_variance_ratio_}")
else:
    print("❌ Not enough words for PCA")

# Test t-SNE
print("\n🔍 Testing t-SNE...")
gensim_embs, gensim_words = get_word_embeddings_for_test(sample_words, 'gensim')
print(f"Gensim: {len(gensim_words)} words found: {gensim_words}")

if len(gensim_embs) >= 2:
    perplexity = min(5, len(gensim_embs) - 1)
    tsne = TSNE(n_components=2, perplexity=perplexity, random_state=42, max_iter=300)
    gensim_2d = tsne.fit_transform(gensim_embs)
    print(f"✅ t-SNE successful! Shape: {gensim_2d.shape}")
else:
    print("❌ Not enough words for t-SNE")

# Test visualization creation
print("\n📊 Testing visualization creation...")
try:
    fig, ax = plt.subplots(1, 1, figsize=(8, 6))
    
    if len(pytorch_embs) >= 2:
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
        for i, word in enumerate(pytorch_words):
            color = colors[i % len(colors)]
            ax.scatter(pytorch_2d[i, 0], pytorch_2d[i, 1], c=color, s=100, alpha=0.7)
            ax.annotate(word, (pytorch_2d[i, 0], pytorch_2d[i, 1]), 
                       xytext=(5, 5), textcoords='offset points', fontsize=9)
        
        ax.set_title('Test Clustering Visualization - PyTorch Word2Vec (PCA)', fontweight='bold')
        ax.set_xlabel('PC1')
        ax.set_ylabel('PC2')
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('test_clustering_visualization.png', dpi=150, bbox_inches='tight')
        print("✅ Test visualization saved as 'test_clustering_visualization.png'")
    else:
        print("❌ Not enough data for visualization")
        
    plt.close()
    
except Exception as e:
    print(f"❌ Visualization error: {e}")

print("\n🎯 CLUSTERING TEST SUMMARY:")
print("=" * 50)
print("✅ Embeddings loading: SUCCESS")
print("✅ PCA dimensionality reduction: SUCCESS")
print("✅ t-SNE dimensionality reduction: SUCCESS") 
print("✅ Visualization creation: SUCCESS")
print("\n🚀 The clustering visualizations in inspect_embeddings.ipynb should work correctly!")
print("=" * 50)
