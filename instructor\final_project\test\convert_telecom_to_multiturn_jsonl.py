import json
from datasets import load_dataset
from collections import defaultdict

# Load the dataset
dataset = load_dataset("talkmap/telecom-conversation-corpus", split="train")

# Group messages by conversation_id
conversations = defaultdict(list)
for item in dataset:
    conversations[item["conversation_id"]].append(item)

# Sort messages in each conversation by date_time
for conv_id in conversations:
    conversations[conv_id].sort(key=lambda x: x["date_time"])

# Prepare and write to JSONL
with open("telecom_conversations_multiturn.jsonl", "w", encoding="utf-8") as f:
    for conv_id, messages in conversations.items():
        turns = [{"speaker": m["speaker"], "text": m["text"]} for m in messages]
        json_obj = {
            "conversation_id": conv_id,
            "turns": turns
        }
        f.write(json.dumps(json_obj, ensure_ascii=False) + "\n")
