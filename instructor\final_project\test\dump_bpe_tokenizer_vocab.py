import sys
from bpe_tokenizer import BPETokenizer

# Usage: python dump_bpe_tokenizer_vocab.py <tokenizer_pickle> <output_file>

def main():
    if len(sys.argv) != 3:
        print("Usage: python dump_bpe_tokenizer_vocab.py <tokenizer_pickle> <output_file>")
        sys.exit(1)
    tokenizer_pickle = sys.argv[1]
    output_file = sys.argv[2]

    tokenizer = BPETokenizer.load(tokenizer_pickle)
    id2token = tokenizer.id2token
    with open(output_file, 'w', encoding='utf-8') as f:
        for idx in sorted(id2token.keys()):
            f.write(f"{idx}\t{id2token[idx]}\n")
    print(f"Dumped {len(id2token)} tokens to {output_file}")

if __name__ == "__main__":
    main()
