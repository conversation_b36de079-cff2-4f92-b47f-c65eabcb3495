"""
<PERSON><PERSON>t to generate a simple conversation between two characters from a short story, using language suitable for 3-4 year-old children.
If a conversation cannot be generated, returns an empty conversation.
"""
from langchain_core.prompts import ChatPromptTemplate
from langchain_ollama.llms import OllamaLLM
from langchain_core.output_parsers import JsonOutputParser

# Define the output schema as a JSON structure
output_schema =   [
        {"name": "str", "text": "str"}
    
    ]

# Create a parser for the expected output
parser = JsonOutputParser()

# Prompt template for structured output
prompt = ChatPromptTemplate.from_template(
    """
Given the following short story, extract two characters and create 
a conversation between them with at most 6 rounds. The conversation should 
use language that is understandable by 3-4 year-old children. 
If you cannot create such a conversation, return an empty conversation.

Story:
{story}

Return only the JSON in the format:
[
    {{"name": "...", "text": "..."}},
    ...
  ]
"""
)

# Connect to Ollama Llama model
model = OllamaLLM(model="gemma3")

# Chain prompt and model
chain = prompt | model | parser

def generate_conversation(story):
    result = chain.invoke({"story": story})
    return result

if __name__ == "__main__":
    # Example usage
    story = "Once upon a time, a little cat named Mia and a puppy named <PERSON> played in the garden. They found a red ball and took turns rolling it."
    conversation = generate_conversation(story)
    print("Generated conversation:")
    for turn in conversation:
        print(f"{turn['name']}: {turn['text']}")
