---
title: "Assignment 3: Sentiment Classification with Neural Networks"
format: 
  html:
    toc: true
    toc-title: Contents
    toc-depth: 4
    self-contained: true
    number-sections: false
jupyter: python3
---


print("\n========== Loading Dataset ==========")
from datasets import load_dataset

dataset = load_dataset('financial_phrasebank', 'sentences_50agree', trust_remote_code=True)
print("Dataset loaded. Example:", dataset['train'][:5])
