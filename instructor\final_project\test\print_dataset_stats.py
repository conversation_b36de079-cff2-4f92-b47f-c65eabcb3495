from datasets import load_dataset
import numpy as np
from tqdm import tqdm

print("Loading dataset 'roneneldan/TinyStories' with streaming...")
dataset = load_dataset("roneneldan/TinyStories", split="train", streaming=True)

char_lengths = []
word_counts = []

# Estimate total number of records for tqdm progress bar
# If not available, tqdm will just show indeterminate progress
try:
    total = load_dataset("roneneldan/TinyStories", split="train").num_rows
except Exception:
    total = None

for item in tqdm(dataset, desc="Processing records", total=total):
    text = item['text']
    char_lengths.append(len(text))
    word_counts.append(len(text.split()))

def print_stats(arr, label):
    arr = np.array(arr)
    print(f"{label} stats:")
    print(f"  Min: {arr.min()}")
    print(f"  25%: {np.percentile(arr, 25)}")
    print(f"  Median: {np.median(arr)}")
    print(f"  Mean: {arr.mean():.2f}")
    print(f"  75%: {np.percentile(arr, 75)}")
    print(f"  Max: {arr.max()}")
    print()

print_stats(char_lengths, "Character length")
print_stats(word_counts, "Word count")
