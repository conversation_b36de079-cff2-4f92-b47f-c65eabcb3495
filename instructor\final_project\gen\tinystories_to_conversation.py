from datasets import load_dataset
import json
import os
import pickle
import random
import hashlib
from tqdm import tqdm
import argparse

from generate_conversation import generate_conversation  # Assuming this function is defined in generate_conversation.py

def convert_and_save(dataset_split, output_path):
    with open(output_path, "w", encoding="utf-8") as f:
        for sample in tqdm(dataset_split, desc=f"Converting to {output_path}"):
            try:
                conversation = generate_conversation(sample)
                if conversation:  # Only save non-empty conversations
                    json.dump(conversation, f, ensure_ascii=False)
                    f.write("\n")
            except Exception as e:
                print(f"Skipping sample due to error: {e}")

def sample_and_generate(dataset_split, output_pkl, sample_size=1000):
    # Step 0: Load existing pickle if it exists
    if os.path.exists(output_pkl):
        with open(output_pkl, 'rb') as pf:
            existing_data = pickle.load(pf)
        print(f"Loaded {len(existing_data)} existing records from {output_pkl}")
    else:
        existing_data = {}

    # Step 1: Filter out existing records and sample new ones
    new_samples = []
    for sample in tqdm(dataset_split, desc="Filtering and sampling"):
        # Add randomness: skip 70% of records
        if random.random() > 0.3:
            continue
        # Use the story text itself as a unique key (stable hash)
        story_text = sample['text']
        if not story_text:
            continue
        sample_id = hashlib.md5(story_text.encode('utf-8')).hexdigest()
        if sample_id not in existing_data:
            new_samples.append((sample_id, sample))
        if len(new_samples) >= sample_size:
            break

    print(f"Generating conversations for {len(new_samples)} new samples...")
    # Step 2: Generate and save
    for sample_id, sample in tqdm(new_samples, desc="Generating conversations"):
        try:
            conversation = generate_conversation(sample)
            if conversation:
                existing_data[sample_id] = conversation
        except Exception as e:
            print(f"Skipping sample {sample_id} due to error: {e}")

    with open(output_pkl, 'wb') as pf:
        pickle.dump(existing_data, pf)
    print(f"Saved {len(existing_data)} records to {output_pkl}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Convert TinyStories to conversation format.")
    parser.add_argument('--split', choices=['train', 'validation', 'valid'], default='train', help='Dataset split to process (train or validation)')
    args = parser.parse_args()

    split_name = 'validation' if args.split in ['validation', 'valid'] else 'train'
    print(f"Loading dataset 'roneneldan/TinyStories' split: {split_name} ...")
    dataset = load_dataset("roneneldan/TinyStories", streaming=True)

    if split_name == 'train':
        print("Processing train split...")
        sample_and_generate(dataset["train"], "tinystories_train_conversations.pkl")
    else:
        print("Processing validation split...")
        sample_and_generate(dataset["validation"], "tinystories_validation_conversations.pkl")

    print("Done.")
