from datasets import load_dataset, DatasetDict
from huggingface_hub import <PERSON><PERSON><PERSON><PERSON>, DatasetCardData
import argparse

# Paths to your JSONL files
tain_path = "tinystories_train_conversations.jsonl"
valid_path = "tinystories_validation_conversations.jsonl"


def main():
    parser = argparse.ArgumentParser(
        description="Upload TinyStories conversations to Hugging Face Hub."
    )
    parser.add_argument(
        "--username", required=True, help="Your Hugging Face username"
    )
    args = parser.parse_args()
    username = args.username

    # Prepare README content
    readme_content = f"""
# TinyStories Conversations

## Dataset Description
This dataset contains multi-turn conversations extracted from the TinyStories corpus, formatted for conversational AI and language modeling tasks.

## Data Fields
- `conversation`: List of utterances in the conversation.
- `metadata`: (Optional) Additional metadata for each conversation.

## Data Splits
- `train`: Training set
- `validation`: Validation set

## Source
TinyStories dataset, processed for conversational modeling.

## Usage

```python
from datasets import load_dataset

dataset = load_dataset(\"{username}/tinystories-conversations\")
print(dataset[\"train\"][0])
```
"""

    # Load datasets
    train_dataset = load_dataset("json", data_files=tain_path)["train"]
    valid_dataset = load_dataset("json", data_files=valid_path)["train"]

    # Create DatasetDict
    dataset_dict = DatasetDict(
        {
            "train": train_dataset,
            "valid": valid_dataset,
        }
    )

    # Prepare metadata
    card_data = DatasetCardData(
        language="en",
        license="cc-by-4.0",
        pretty_name="TinyStories Conversations",
        task_categories=["text-generation", "conversational"],
        dataset_info={
            "train": {"n_samples": len(train_dataset)},
            "validation": {"n_samples": len(valid_dataset)},
        },
        dataset_features=[
            {
                "name": "conversation",
                "dtype": "list",
                "description": "List of utterances in the conversation",
            },
            {
                "name": "metadata",
                "dtype": "dict",
                "description": "Optional metadata for each conversation",
            },
        ],
    )

    # Push to Hugging Face Hub
    repo_id = f"{username}/tinystories-conversations"
    dataset_dict.push_to_hub(
        repo_id
    )

    # Optionally, add tags using the API
    # api = HfApi()
    # api.add_tag(
    #     repo_id=repo_id, tag="conversational", repo_type="dataset"
    # )


if __name__ == "__main__":
    main()
