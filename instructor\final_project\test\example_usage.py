import torch
from transformer_model import TinyS<PERSON>iesConfig, TinyStoriesForCausalLM

def main():
    """Example usage of the TinyStories model."""
    
    # Create a small model configuration (similar to the paper's smallest models)
    config = TinyStoriesConfig(
        vocab_size=10000,
        hidden_size=256,
        num_hidden_layers=4,  # Can be as low as 1 according to the paper
        num_attention_heads=8,
        intermediate_size=1024,
        max_position_embeddings=512,
    )
    
    # Initialize the model
    model = TinyStoriesForCausalLM(config)
    
    # Print model architecture and parameter count
    print(f"Model architecture:\n{model}")
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    print(f"\nTotal parameters: {total_params:,}")
    
    # Example of text generation (with random inputs since we don't have a tokenizer)
    # In a real scenario, you would use a proper tokenizer
    print("\nExample text generation (with random token IDs):")
    
    # Create a batch of random token IDs (simulating tokenized input)
    batch_size = 1
    seq_length = 10
    input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_length))
    
    # Generate text
    print(f"Input shape: {input_ids.shape}")
    with torch.no_grad():
        output_ids = model.generate(
            input_ids=input_ids,
            max_length=50,
            num_beams=1,  # Greedy decoding
            temperature=1.0,
        )
    
    print(f"Output shape: {output_ids.shape}")
    print("Generated token IDs:")
    print(output_ids)
    
    # In a real scenario, you would decode the token IDs back to text using a tokenizer
    print("\nNote: To properly use this model, you would need to:")
    print("1. Initialize it with pre-trained weights or train it on the TinyStories dataset")
    print("2. Use a proper tokenizer (like the GPT-Neo tokenizer mentioned in the paper)")
    print("3. Decode the generated token IDs back to text using the tokenizer")

if __name__ == "__main__":
    main()
