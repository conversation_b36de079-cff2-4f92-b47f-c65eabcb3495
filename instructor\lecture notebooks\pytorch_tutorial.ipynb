{"cells": [{"cell_type": "raw", "id": "8994633e", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "title: \"Pytorch tutorial: Tensors, Autograd, and the Power of GPU Training\"\n", "format: \n", "  html:\n", "    toc: true\n", "    toc-title: Contents\n", "    toc-depth: 4\n", "    self-contained: true\n", "    number-sections: true\n", "jupyter: python3\n", "---"]}, {"cell_type": "markdown", "id": "647c9b62", "metadata": {}, "source": ["PyTorch was developed by Meta (formerly Facebook) and has become one of the most trusted frameworks for machine learning. It’s known for its flexibility, intuitive Pythonic design, and powerful tools such as tensors, autograd, and GPU acceleration.\n", "\n", "In this tutorial, we'll break down the key concepts of PyTorch step by step to help you build a solid foundation."]}, {"cell_type": "code", "execution_count": 54, "id": "90aed58a", "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.data import DataLoader, TensorDataset, Dataset\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.datasets import make_classification, make_regression\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "markdown", "id": "e56ae906", "metadata": {}, "source": ["# <PERSON>yTorch Tensors - The Foundation\n", "\n", "Tensors are the fundamental data structures in PyTorch. Think of them as NumPy arrays but **can run on GPU and track gradients**\n", "\n", "Tensors are how data flows through neural networks—so understanding them is key.\n", "\n", "## Creating Tensors"]}, {"cell_type": "code", "execution_count": 55, "id": "6996eaa0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.1 Creating Tensors:\n", "From list: tensor([1, 2, 3, 4, 5])\n", "Zeros tensor shape: torch.Size([3, 4])\n", "Ones tensor shape: torch.Size([2, 3])\n", "Random tensor (normal):\n", "tensor([[-0.5229, -0.2176, -1.8557],\n", "        [ 0.0830,  0.1368,  0.1284]])\n", "Random tensor (uniform):\n", "tensor([[0.9386, 0.8609, 0.7210],\n", "        [0.7142, 0.6046, 0.7022]])\n", "From NumPy: tensor([1, 2, 3, 4], dtype=torch.int32)\n"]}], "source": ["# Creating tensors\n", "print(\"1.1 Creating Tensors:\")\n", "# From lists\n", "tensor_from_list = torch.tensor([1, 2, 3, 4, 5])\n", "print(f\"From list: {tensor_from_list}\")\n", "\n", "# Zeros and ones\n", "zeros_tensor = torch.zeros(3, 4)\n", "ones_tensor = torch.ones(2, 3)\n", "print(f\"Zeros tensor shape: {zeros_tensor.shape}\")\n", "print(f\"Ones tensor shape: {ones_tensor.shape}\")\n", "\n", "# Random tensors\n", "random_tensor = torch.randn(2, 3)  # Normal distribution\n", "uniform_tensor = torch.rand(2, 3)  # Uniform [0,1]\n", "print(f\"Random tensor (normal):\\n{random_tensor}\")\n", "print(f\"Random tensor (uniform):\\n{uniform_tensor}\")\n", "\n", "# From NumPy\n", "numpy_array = np.array([1, 2, 3, 4])\n", "tensor_from_numpy = torch.from_numpy(numpy_array)\n", "print(f\"From NumPy: {tensor_from_numpy}\")"]}, {"cell_type": "markdown", "id": "e75203ac", "metadata": {}, "source": ["## Tensor Properties"]}, {"cell_type": "code", "execution_count": 56, "id": "719361ab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "1.2 Tensor Properties:\n", "Shape: torch.<PERSON><PERSON>([3, 4, 5])\n", "Data type: torch.float32\n", "Device: cpu\n", "Number of dimensions: 3\n", "Total elements: 60\n"]}], "source": ["print(\"\\n1.2 Tensor Properties:\")\n", "sample_tensor = torch.randn(3, 4, 5)\n", "print(f\"Shape: {sample_tensor.shape}\")\n", "print(f\"Data type: {sample_tensor.dtype}\")\n", "print(f\"Device: {sample_tensor.device}\")\n", "print(f\"Number of dimensions: {sample_tensor.ndim}\")\n", "print(f\"Total elements: {sample_tensor.numel()}\")"]}, {"cell_type": "markdown", "id": "6ecbf083", "metadata": {}, "source": ["## Tensor Operations\n", "\n", "### Basic Operations"]}, {"cell_type": "code", "execution_count": 57, "id": "5a480aaf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.3 Element-wise Operations:\n", "a = tensor([1., 2., 3.])\n", "b = tensor([4., 5., 6.])\n", "a + b = tensor([5., 7., 9.])\n", "a - b = tensor([-3., -3., -3.])\n", "a * b = tensor([ 4., 10., 18.])\n", "a / b = tensor([0.2500, 0.4000, 0.5000])\n"]}], "source": ["# Basic operations\n", "a = torch.tensor([1.0, 2.0, 3.0])\n", "b = torch.tensor([4.0, 5.0, 6.0])\n", "\n", "print(\"1.3 Element-wise Operations:\")\n", "print(f\"a = {a}\")\n", "print(f\"b = {b}\")\n", "print(f\"a + b = {a + b}\")\n", "print(f\"a - b = {a - b}\")\n", "print(f\"a * b = {a * b}\")  # Element-wise multiplication\n", "print(f\"a / b = {a / b}\")\n"]}, {"cell_type": "markdown", "id": "bda6cb19", "metadata": {}, "source": ["### Matrix Multiplication in Pytorch\n", "\n", "In PyTorch, **matrix multiplication is the backbone of most tensor computations**. It’s essential for speed, scalability, and expressing the math behind deep learning\n", "\n", "PyTorch supports matrix multiplication using several methods:\n", "\n", "- `@` operator (Pythonic and preferred for readability)\n", "- `torch.matmul()` (handles broadcasting and works for 1D, 2D, and batched tensors)\n", "- `torch.mm()` (only for 2D matrices)\n", "\n", "**Key differences:**\n", "\n", "- `@` and `torch.matmul()` can handle batched multiplication (e.g., 3D tensors).\n", "- `torch.mm()` is limited to 2D tensors and will raise an error for higher dimensions.\n", "\n", "All methods require that the **inner dimensions match**. For example, if `A` has shape `(batch_size, m, n)` and `B` has shape `(batch_size, n, p)`, then `A @ B` returns a tensor of shape `(batch_size, m, p)`.\n", "\n", "Always ensure that the inner dimensions match for matrix multiplication, e.g., `(m × n)` × `(n × p)` → `(m × p)`."]}, {"cell_type": "code", "execution_count": 58, "id": "738fc512", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Matrix1 shape: torch.Size([2, 3])\n", "Matrix2 shape: torch.Size([3, 4])\n", "Result shape: torch.Size([2, 4])\n"]}], "source": ["# Matrix multiplication\n", "matrix1 = torch.randn(2, 3)\n", "matrix2 = torch.randn(3, 4)\n", "result = torch.mm(matrix1, matrix2)  # or matrix1 @ matrix2\n", "print(f\"Matrix1 shape: {matrix1.shape}\")\n", "print(f\"Matrix2 shape: {matrix2.shape}\")\n", "print(f\"Result shape: {result.shape}\")"]}, {"cell_type": "markdown", "id": "adb4da40", "metadata": {}, "source": ["### Reshaping Tensors for Matrix Multiplication\n", "\n", "When tensor dimensions don’t align for matrix multiplication, reshaping is often required. PyTorch provides several methods:\n", "\n", "- `.view(shape)` – Fast and memory-efficient, but only works on contiguous tensors.\n", "- `.reshape(shape)` – More flexible than `.view()`, can handle non-contiguous tensors.\n", "- `.unsqueeze(dim)` – Adds a dimension of size 1 (useful for promoting 1D tensors to 2D).\n", "- `.squeeze(dim)` – Removes dimensions of size 1.\n", "- `.flatten(start_dim, end_dim)` – Flattens dimensions into a single axis.\n"]}, {"cell_type": "code", "execution_count": 59, "id": "e1fe2771", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original shape: torch.Size([2, 3, 4])\n", "Reshaped: torch.<PERSON><PERSON>([6, 4])\n", "Flattened: torch.<PERSON><PERSON>([24])\n"]}], "source": ["# Matrix reshaping\n", "original = torch.randn(2, 3, 4)\n", "reshaped = original.view(6, 4)  # view() creates a new view\n", "flattened = original.flatten()   # flatten() to 1D\n", "print(f\"Original shape: {original.shape}\")\n", "print(f\"Reshaped: {reshaped.shape}\")\n", "print(f\"Flattened: {flattened.shape}\")"]}, {"cell_type": "markdown", "id": "a6a30fbe", "metadata": {}, "source": ["### Tensor Indexing and Slicing in PyTorch\n", "\n", "Tensor indexing and slicing in PyTorch works very similarly to NumPy.\n", "\n", "#### Basic Indexing\n", "- `x[i]` – Access the i-th element along the first dimension.\n", "- `x[i, j]` – Access the element at row `i`, column `j`.\n", "- `x[i, :]` – All columns of row `i`.\n", "- `x[:, j]` – All rows of column `j`.\n", "\n", "#### Slicing\n", "- `x[start:stop]` – Slice along the first dimension.\n", "- `x[:, start:stop]` – Slice along other dimensions.\n", "- `x[..., j]` – Use `...` for flexible indexing (all leading dimensions).\n", "\n", "#### Negative Indices\n", "- `x[-1]` – Last element along the first dimension.\n", "- `x[:, -2:]` – Last two columns.\n", "\n", "#### Boolean Indexing\n", "- `mask = x > 0`  \n", "  `x[mask]` – Elements where the condition is `True`.\n"]}, {"cell_type": "code", "execution_count": 60, "id": "9a40a8fa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original tensor shape: torch.Size([4, 5])\n", "First row: tensor([-0.4053, -1.4587, -1.4042, -1.5694, -0.7642])\n", "First column: tensor([-0.4053, -1.2980, -0.3852, -1.9317])\n", "Submatrix [1:3, 2:4]: <PERSON>.<PERSON><PERSON>([2, 2])\n"]}], "source": ["# Matrix indexing and slicing\n", "tensor = torch.randn(4, 5)\n", "print(f\"Original tensor shape: {tensor.shape}\")\n", "print(f\"First row: {tensor[0]}\")\n", "print(f\"First column: {tensor[:, 0]}\")\n", "print(f\"Submatrix [1:3, 2:4]: {tensor[1:3, 2:4].shape}\")"]}, {"cell_type": "markdown", "id": "02594382", "metadata": {}, "source": ["# Automatic Differentiation (Autograd)\n", "\n", "* Training a model requires calculating gradients. PyTorch handles this automatically using **Autograd**, its built-in automatic differentiation engine.\n", "\n", "* By default, PyTorch does not track operations on tensors. To enable gradient tracking for training, you must explicitly set `requires_grad=True` when creating a tensor.\n", "\n", "* Once enabled, PyTorch records all operations on that tensor to construct a computation graph.\n", "\n", "* Calling `.backward()` method to ask `PyTorch` to calculate the gradients, which are then stored in the `grad` attribute."]}, {"cell_type": "markdown", "id": "ce7f2f1a", "metadata": {}, "source": ["Let's define a simple function and compute its gradients"]}, {"cell_type": "code", "execution_count": 61, "id": "27bb589a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["y = x² + 3x + 1\n", "dy/dx at x=2: tensor([7.])\n"]}], "source": ["# Create a tensor that requires gradients\n", "x = torch.tensor([2.0], requires_grad=True)\n", "\n", "# Define the function\n", "y = x**2 + 3*x + 1\n", "print(f\"y = x² + 3x + 1\")\n", "\n", "# backpropagate to compute the gradient\n", "y.backward()  \n", "\n", "# Print the gradient dy/dx\n", "print(f\"dy/dx at x=2: {x.grad}\")  "]}, {"cell_type": "markdown", "id": "006505af", "metadata": {}, "source": ["Multiple Variables"]}, {"cell_type": "code", "execution_count": 62, "id": "c8b17f0e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["x = tensor([1., 2.], requires_grad=True)\n", "y = tensor([3., 4.], requires_grad=True)\n", "z = sum(x² + y²)\n", "dz/dx = tensor([2., 4.])\n", "dz/dy = tensor([6., 8.])\n"]}], "source": ["# Create two tensors with requires_grad=True so we can compute gradients w.r.t. them\n", "x = torch.tensor([1.0, 2.0], requires_grad=True)\n", "y = torch.tensor([3.0, 4.0], requires_grad=True)\n", "\n", "# Compute a scalar z = x₁² + x₂² + y₁² + y₂²\n", "z = torch.sum(x**2 + y**2)  \n", "\n", "# Print the tensors and the computed scalar\n", "print(f\"x = {x}\")\n", "print(f\"y = {y}\")\n", "print(f\"z = sum(x² + y²)\")\n", "\n", "# Backpropagate to compute gradients dz/dx and dz/dy\n", "z.backward()\n", "\n", "# Print the gradients\n", "print(f\"dz/dx = {x.grad}\")  # Should be [2*x₁, 2*x₂] = [2, 4]\n", "print(f\"dz/dy = {y.grad}\")  # Should be [2*y₁, 2*y₂] = [6, 8]"]}, {"cell_type": "markdown", "id": "d4c37edb", "metadata": {}, "source": ["`grad_fn` enables PyTorch’s **autograd engine** to:\n", "\n", "- Trace the computation graph (a.k.a. dynamic computation graph)\n", "- Perform automatic differentiation (backpropagation)\n"]}, {"cell_type": "code", "execution_count": 71, "id": "a371d84a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["y: tensor([8.3283, 0.3836, 1.0136], grad_fn=<AddBackward0>)\n", "y.grad_fn: <AddBackward0 object at 0x000002C597D7EC80>\n", "y_sum.grad_fn: <SumBackward0 object at 0x000002C597D7EC80>\n", "x.grad (after backward): tensor([ 4.4439, -1.1108,  3.0373])\n", "Grad_fn chain:\n", "   <SumBackward0 object at 0x000002C597D7EC80>\n", "   <AddBackward0 object at 0x000002C59858E8C0>\n", "   <MulBackward0 object at 0x000002C597D7EC80>\n", "   <SinBackward0 object at 0x000002C59858E8C0>\n", "   <AccumulateGrad object at 0x000002C597D7EC80>\n"]}], "source": ["# Demonstrating grad_fn with a complex function\n", "x = torch.tensor([2.0, -1.0, 0.5], requires_grad=True)\n", "\n", "# Define a complex function: y = sin(x) * exp(x) + log(x**2 + 1)\n", "y = torch.sin(x) * torch.exp(x) + torch.log(x**2 + 1)\n", "\n", "print(\"y:\", y)\n", "print(\"y.grad_fn:\", y.grad_fn)  # This shows the grad_fn of the output tensor\n", "\n", "# If y is not a scalar, sum to get a scalar output for backward()\n", "y_sum = y.sum()\n", "print(\"y_sum.grad_fn:\", y_sum.grad_fn)\n", "\n", "y_sum.backward()\n", "print(\"x.grad (after backward):\", x.grad)\n", "\n", "# Show the grad_fn chain for y\n", "def print_grad_fn_chain(tensor):\n", "    fn = tensor.grad_fn\n", "    chain = []\n", "    while fn is not None:\n", "        chain.append(str(fn))\n", "        if hasattr(fn, 'next_functions') and fn.next_functions:\n", "            fn = fn.next_functions[0][0]\n", "        else:\n", "            break\n", "    print(\"Grad_fn chain:\")\n", "    for f in chain:\n", "        print(\"  \", f)\n", "\n", "print_grad_fn_chain(y_sum)"]}, {"cell_type": "markdown", "id": "cc0c0350", "metadata": {}, "source": ["## Gradient Accumulation in PyTorch\n", "\n", "- By default, **PyTorch accumulates gradients** in `.grad` fields — it does **not** overwrite them.\n", "- This is also the reason why we need to run `zero_grad()` in every training iteration (more on this later). Otherwise our gradients would keep building up from one training iteration to the other, which would cause our updates to be wrong.\n", "- \n", "### When to call `zero_grad()`\n", "\n", "For standard (non-accumulation) training loops:\n", "\n", "```python\n", "for batch in dataloader:\n", "    optimizer.zero_grad()   # Clear old gradients\n", "    loss = model(batch)\n", "    loss.backward()         # Compute new gradients\n", "    optimizer.step()        # Update model parameters\n", "\n", "```\n", "\n", "Here is an example:"]}, {"cell_type": "code", "execution_count": 74, "id": "cd1860d0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["After first backward: x.grad = tensor([2.])\n"]}], "source": ["x = torch.tensor([1.0], requires_grad=True)\n", "\n", "# First computation\n", "y1 = x**2\n", "y1.backward()\n", "print(f\"After first backward: x.grad = {x.grad}\")"]}, {"cell_type": "code", "execution_count": 75, "id": "cb27de95", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["After second backward: x.grad = tensor([5.])\n"]}], "source": ["# Second computation (gradients accumulate!)\n", "y2 = x**3\n", "y2.backward()\n", "print(f\"After second backward: x.grad = {x.grad}\")"]}, {"cell_type": "markdown", "id": "d3d042d7", "metadata": {}, "source": ["On the second backward pass, `x.grad` becomes `[5.]` because it **adds** the new gradient `3` (from `x³`) to the previous gradient `2` — so `2 + 3 = 5`.\n", "\n", "✅ **Tip**: To avoid unintended accumulation, reset gradients between steps with:"]}, {"cell_type": "code", "execution_count": 39, "id": "bc7b608f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["After zeroing: x.grad = tensor([0., 0.])\n"]}], "source": ["# Clear gradients\n", "x.grad.zero_()\n", "print(f\"After zeroing: x.grad = {x.grad}\")"]}, {"cell_type": "markdown", "id": "a86deb55", "metadata": {}, "source": ["### When to Use Gradient Accumulation\n", "\n", "**Gradient accumulation** is useful when your model or hardware cannot handle large batch sizes due to memory constraints.\n", "\n", "Instead of updating model weights after every mini-batch, you can:\n", "1. Perform multiple forward/backward passes over smaller mini-batches.\n", "2. Accumulate gradients across these steps.\n", "3. Call `optimizer.step()` once after several steps to simulate a larger batch update.\n", "\n", "**Example:**  \n", "If your target batch size is 64, but your GPU can only handle batch size 16, you can accumulate gradients over 4 steps:\n", "\n", "```python\n", "accum_steps = 4\n", "\n", "for i, batch in enumerate(dataloader):\n", "    loss = model(batch)\n", "    loss.backward()  # Accumulate gradients\n", "\n", "    if (i + 1) % accum_steps == 0:\n", "        optimizer.step()        # Update model weights\n", "        optimizer.zero_grad()   # Clear gradients for next accumulation cycle\n", "\n", "```"]}, {"cell_type": "markdown", "id": "2e450b73", "metadata": {}, "source": ["# Data Loading and Preprocessing\n", "\n", "PyTorch provides flexible tools for preparing and loading data, especially for training machine learning models. \n", "\n", "##  Using `TensorDataset`\n", "\n", "Use when your input data and labels are already in tensors."]}, {"cell_type": "markdown", "id": "f6d9c76b", "metadata": {}, "source": ["Let's first convert your data into tensors"]}, {"cell_type": "code", "execution_count": 14, "id": "5f01d785", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating Datasets:\n", "Data shape: torch.Size([1000, 10])\n", "Labels shape: torch.Size([1000])\n", "Label distribution: tensor([501, 499])\n"]}], "source": ["print(\"Creating Datasets:\")\n", "# Generate synthetic data\n", "X, y = make_classification(n_samples=1000, n_features=10, n_classes=2, random_state=42)\n", "\n", "# Convert to tensors\n", "X_tensor = torch.FloatTensor(X)\n", "y_tensor = torch.LongTensor(y)\n", "\n", "print(f\"Data shape: {X_tensor.shape}\")\n", "print(f\"Labels shape: {y_tensor.shape}\")\n", "print(f\"Label distribution: {torch.bincount(y_tensor)}\")"]}, {"cell_type": "markdown", "id": "2518df02", "metadata": {}, "source": ["Then use `TensorDataset` to package your input and target tensors together."]}, {"cell_type": "code", "execution_count": 15, "id": "d4ab1f5b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TensorDataset\n", "Dataset length: 1000\n"]}], "source": ["print(\"TensorDataset\")\n", "# Create dataset\n", "dataset = TensorDataset(X_tensor, y_tensor)\n", "print(f\"Dataset length: {len(dataset)}\")"]}, {"cell_type": "markdown", "id": "0029fa8c", "metadata": {}, "source": ["## DataL<PERSON>der\n", "\n", "Then use DataLoader to efficiently iterate through the data in batches, optionally shuffled and loaded in parallel.\n", "\n", "1. <PERSON><PERSON>\n", "- `TensorDataset` just holds the data.\n", "- `DataLoader` divides it into batches, which is crucial for training neural networks efficiently using mini-batch gradient descent.\n", "\n", "2. <PERSON><PERSON>\n", "- You can enable shuffling of data each epoch (e.g., `shuffle=True`), which helps improve generalization.\n", "\n", ". <PERSON><PERSON><PERSON>\n", "- `DataLoader` can use multiple worker threads (`num_workers`) to load data in parallel, improving performance for large datasets.\n", "\n", "4. Automatic Iteration\n", "- It returns an iterator, so you can use it in a `for` loop easily during training:\n", "\n", "```python\n", "for X_batch, y_batch in train_loader:\n", "```"]}, {"cell_type": "code", "execution_count": 16, "id": "81556cc0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of batches: 32\n", "Batch 1: X=torch.<PERSON><PERSON>([32, 10]), y=torch.<PERSON><PERSON>([32])\n", "Batch 2: X=torch.<PERSON><PERSON>([32, 10]), y=torch.<PERSON><PERSON>([32])\n", "Batch 3: X=torch.<PERSON><PERSON>([32, 10]), y=torch.<PERSON><PERSON>([32])\n"]}], "source": ["# Create data loader\n", "batch_size = 32\n", "dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)\n", "\n", "print(f\"Number of batches: {len(dataloader)}\")\n", "\n", "# Iterate through batches\n", "for batch_idx, (batch_x, batch_y) in enumerate(dataloader):\n", "    print(f\"Batch {batch_idx + 1}: X={batch_x.shape}, y={batch_y.shape}\")\n", "    if batch_idx == 2:  # Show first 3 batches\n", "        break"]}, {"cell_type": "markdown", "id": "a9b33644", "metadata": {}, "source": ["## Create a Custom Dataset\n", "\n", "Use when loading from files, applying preprocessing, or handling complex logic.\n", "\n", "\n", "`torch.utils.data.Dataset` is an **abstract class** representing a dataset.  \n", "To create your own dataset, subclass `Dataset` and override the following two methods:\n", "\n", "- `__len__`: Returns the size of the dataset, so that `len(dataset)` works.\n", "- `__getitem__`: Supports indexing like `dataset[i]` to retrieve the *i-th* sample."]}, {"cell_type": "code", "execution_count": 17, "id": "9054dd6e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Custom Dataset:\n", "Custom dataset length: 1000\n", "Custom dataloader batches: 16\n"]}], "source": ["print(\"Custom Dataset:\")\n", "\n", "from torch.utils.data import Dataset\n", "\n", "# Example of custom dataset class\n", "class SimpleDataset(Dataset):\n", "    def __init__(self, X, y):\n", "        self.X = X\n", "        self.y = y\n", "    \n", "    def __len__(self):\n", "        return len(self.X)\n", "    \n", "    def __getitem__(self, idx):\n", "        return self.X[idx], self.y[idx]\n", "\n", "# Create custom dataset\n", "custom_dataset = SimpleDataset(X_tensor, y_tensor)\n", "custom_dataloader = DataLoader(custom_dataset, batch_size=64, shuffle=True)\n", "\n", "print(f\"Custom dataset length: {len(custom_dataset)}\")\n", "print(f\"Custom dataloader batches: {len(custom_dataloader)}\")"]}, {"cell_type": "markdown", "id": "f9c0a9de", "metadata": {}, "source": ["## Feature Scaling (Normalization)\n", "\n", "- **Normalization improves training**: Neural networks converge faster and more reliably when input features are on a similar scale.\n", "\n", "- **Especially important** for models like neural nets.\n", "\n", "- Helps with **gradient stability** and **regularization**.\n"]}, {"cell_type": "code", "execution_count": 18, "id": "90af60bb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data Preprocessing:\n", "Before normalization:\n", "Mean: tensor([ 0.0297, -0.0152,  0.0215, -0.0274, -0.0365])\n", "Std: tensor([0.8590, 1.0013, 0.8181, 0.9946, 1.0377])\n", "\n", "After normalization:\n", "Mean: tensor([ 1.9073e-09,  1.8120e-08,  1.0490e-08, -1.9073e-09,  1.2398e-08])\n", "Std: tensor([1., 1., 1., 1., 1.])\n"]}], "source": ["print(\"Data Preprocessing:\")\n", "# Normalization example\n", "print(\"Before normalization:\")\n", "print(f\"Mean: {X_tensor.mean(dim=0)[:5]}\")  # Show first 5 features\n", "print(f\"Std: {X_tensor.std(dim=0)[:5]}\")\n", "\n", "# Normalize\n", "normalized_X = (X_tensor - X_tensor.mean(dim=0)) / X_tensor.std(dim=0)\n", "print(\"\\nAfter normalization:\")\n", "print(f\"Mean: {normalized_X.mean(dim=0)[:5]}\")\n", "print(f\"Std: {normalized_X.std(dim=0)[:5]}\")"]}, {"cell_type": "markdown", "id": "580e3d8c", "metadata": {}, "source": ["# Neural Network Modules in PyTorch\n", "\n", "PyTorch provides the `torch.nn` module to help you build and manage neural networks efficiently.\n", "\n", "At the core is the `nn.Module` class, which you subclass to define your own models.\n", "\n", "## Define Your Neural Network Architecture\n", "\n", "- **`nn.<PERSON>`** is the base class for all neural network models.  \n", "  Your model class must inherit from it.\n", "\n", "- In the constructor (`__init__`), define the layers and activation functions.  \n", "  Typically, layers and activations alternate (e.g., `Linear → ReLU → Linear`),  \n", "  except for the final layer which often omits activation depending on the task.\n", "\n", "- The **forward pass** is defined in the `forward()` method,  \n", "  which specifies how the input data flows through the layers.\n", "\n", " \n", " \n", "Example: A Simple Feedforward Network\n"]}, {"cell_type": "code", "execution_count": 19, "id": "70f3358a", "metadata": {}, "outputs": [], "source": ["\n", "import torch.nn as nn\n", "\n", "class SimpleNN(nn.Module):\n", "    def __init__(self, input_dim, hidden_dim, output_dim):\n", "        super().__init__()\n", "        self.fc1 = nn.Linear(input_dim, hidden_dim)  # First linear layer\n", "        self.relu = nn.ReLU()                        # Activation\n", "        self.fc2 = nn.Linear(hidden_dim, output_dim) # Output layer\n", "\n", "    def forward(self, x):\n", "        x = self.fc1(x)\n", "        x = self.relu(x)\n", "        x = self.fc2(x)\n", "        return x"]}, {"cell_type": "markdown", "id": "dfb14a14", "metadata": {}, "source": ["### Common Layers in `torch.nn`\n", "\n", "| Layer Type       | Description                               |\n", "|------------------|-------------------------------------------|\n", "| `nn.Linear`      | Fully connected (dense) layer             |\n", "| `nn.ReLU`        | Activation function (non-linear)          |\n", "| `nn.RNN`         | Basic recurrent layer for sequences       |\n", "| `nn.LSTM`        | Recurrent layer for sequences             |\n", "| `nn.Dropout`     | Randomly zeroes elements during training  |\n", "| `nn.<PERSON>`   | Normalizes input for stable training      |"]}, {"cell_type": "markdown", "id": "4ba9f027", "metadata": {}, "source": ["### Common Activation Functions\n", "\n", "- **Layers** (e.g., `Linear`, `Conv2d`) perform affine transformations:\n", "\n", " $$\n", "  \\text{output} = W x + b\n", " $$\n", "\n", "- Without a **non-linear activation**, stacking multiple layers is equivalent to a single linear transformation — the model won't be able to learn complex patterns.\n", "\n", "- **Activation functions** introduce non-linearity, allowing the model to approximate complex functions and decision boundaries.\n", "\n", "\n", "| Function       | PyTorch Module         | Description                          |\n", "|----------------|------------------------|--------------------------------------|\n", "| ReLU           | `nn.ReLU()`            | Most widely used; replaces negatives with 0 |\n", "| Sigmoid        | `nn.Sigmoid()`         | Squashes input to (0, 1); used in binary classification |\n", "| Tanh           | `nn.Tanh()`            | Squashes input to (-1, 1); zero-centered |\n", "| Leaky ReLU     | `nn.LeakyReLU()`       | Like ReLU but allows small gradient for negatives |\n", "| Softmax        | `nn.Softmax(dim=1)`    | Converts output to probability distribution (multiclass classification) |\n", "| GELU           | `nn.GELU()`            | Used in modern transformers; smooth alternative to ReLU |\n"]}, {"cell_type": "markdown", "id": "e2f06d80", "metadata": {}, "source": ["Instantiate the neural network model with:"]}, {"cell_type": "code", "execution_count": 20, "id": "aa1fd0f6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SimpleNN(\n", "  (fc1): Linear(in_features=100, out_features=64, bias=True)\n", "  (relu): ReLU()\n", "  (fc2): Linear(in_features=64, out_features=10, bias=True)\n", ")\n"]}], "source": ["# - input dimension = 100\n", "# - hidden layer size = 64\n", "# - output dimension = 10 (e.g., for 10-class classification)\n", "model = SimpleNN(100, 64, 10)\n", "\n", "# Print the model architecture\n", "print(model)"]}, {"cell_type": "markdown", "id": "aa4b5e18", "metadata": {}, "source": ["output the model's architecture:"]}, {"cell_type": "code", "execution_count": 21, "id": "c9ba65cd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----------------------------------------------------------------\n", "        Layer (type)               Output Shape         Param #\n", "================================================================\n", "            Linear-1                   [-1, 64]           6,464\n", "              ReLU-2                   [-1, 64]               0\n", "            Linear-3                   [-1, 10]             650\n", "================================================================\n", "Total params: 7,114\n", "Trainable params: 7,114\n", "Non-trainable params: 0\n", "----------------------------------------------------------------\n", "Input size (MB): 0.00\n", "Forward/backward pass size (MB): 0.00\n", "Params size (MB): 0.03\n", "Estimated Total Size (MB): 0.03\n", "----------------------------------------------------------------\n"]}], "source": ["# use torchsummary to get a summary of the model\n", "try:\n", "    from torchsummary import summary\n", "    summary(model, input_size=(100,))\n", "except ImportError:\n", "    print(\"torchsummary not installed. Skipping model summary.\")\n", "    print(\"You can install it using: pip install torchsummary\")"]}, {"cell_type": "markdown", "id": "45388648", "metadata": {}, "source": ["### Using `nn.Sequential` in PyTorch\n", "\n", "For simple feedforward networks where layers are applied **in a fixed, linear order** (e.g., `Linear → ReLU → Linear`), `nn.Sequential` is a convenient and concise choice.\n"]}, {"cell_type": "code", "execution_count": 22, "id": "670fba39", "metadata": {}, "outputs": [], "source": ["import torch.nn as nn\n", "\n", "model = nn.Sequential(\n", "    nn.<PERSON><PERSON>(100, 64),\n", "    nn.ReLU(),\n", "    nn.<PERSON><PERSON>(64, 10)\n", ")"]}, {"cell_type": "markdown", "id": "1f161bbd", "metadata": {}, "source": ["However, `nn.Sequential` is **not suitable** when:\n", "\n", "- You need **conditionals**, **loops**, or **multiple inputs/outputs** in the `forward()` pass.\n", "- You want to **reuse layers** or apply layers in **non-sequential ways**.\n", "\n", "In such cases, define a custom model by subclassing `nn.Module`, which gives you full control over the forward logic."]}, {"cell_type": "markdown", "id": "61334c02", "metadata": {}, "source": ["# Loss Functions and Optimization\n", "\n", "## Loss Functions\n", "\n", "\n", "Loss functions (also called **criteria**) measure how well the model's predictions match the true labels.  \n", "They guide the model during training by providing the gradient used in backpropagation.\n", "\n", "You can define a loss function in PyTorch using the `torch.nn` module:\n", "\n", "```python\n", "import torch.nn as nn\n", "\n", "criterion = nn.CrossEntropyLoss()\n", "```\n", "\n", "###  Commonly Used Loss Functions in PyTorch (Preferred)\n", "\n", "| Task                      | Loss Function              | Output Shape                     | Notes                                                  |\n", "|---------------------------|----------------------------|----------------------------------|--------------------------------------------------------|\n", "| Binary classification     | `nn.BCEWithLogitsLoss()`   | `(batch_size, 1)`                |Combines `Sigmoid` + `BCELoss`             |\n", "| Multiclass classification | `nn.CrossEntropyLoss()`    | `(batch_size, num_classes)`      |Includes `LogSoftmax` internally           |\n", "| Regression                | `nn.MSELoss()`             | `(batch_size, 1)`                | Mean Squared Error (default choice for regression)     |\n", "| Regression                | `nn.L1Loss()`              | `(batch_size, 1)`                | Mean Absolute Error, more robust to outliers           |\n"]}, {"cell_type": "markdown", "id": "05b74653", "metadata": {}, "source": ["For classification"]}, {"cell_type": "code", "execution_count": 23, "id": "75cbc97f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["How to define loss functions and optimizers\n", "------------------------------------------------------------\n", "5.1 Common Loss Functions:\n", "Predictions shape: torch.<PERSON><PERSON>([4, 3])\n", "True labels: tensor([0, 1, 2, 1])\n", "Cross-entropy loss function: CrossEntropyLoss()\n", "Cross-entropy loss: 1.0840\n"]}], "source": ["print(\"How to define loss functions and optimizers\")\n", "print(\"-\" * 60)\n", "\n", "print(\"5.1 Common Loss Functions:\")\n", "# Generate some dummy data\n", "batch_size = 4\n", "num_classes = 3\n", "\n", "# For classification\n", "predictions = torch.randn(batch_size, num_classes)\n", "true_labels = torch.tensor([0, 1, 2, 1])  # class indices\n", "\n", "print(f\"Predictions shape: {predictions.shape}\")\n", "print(f\"True labels: {true_labels}\")\n", "\n", "# Cross-entropy loss (for classification)\n", "ce_loss = nn.CrossEntropyLoss()  # Automatically applies softmax\n", "print(f\"Cross-entropy loss function: {ce_loss}\")\n", "loss_value = ce_loss(predictions, true_labels)\n", "print(f\"Cross-entropy loss: {loss_value:.4f}\")"]}, {"cell_type": "markdown", "id": "7c05dd55", "metadata": {}, "source": ["For regression"]}, {"cell_type": "code", "execution_count": 24, "id": "d501062a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MSE loss: 0.8765\n", "MAE loss: 0.8396\n"]}], "source": ["# For regression\n", "pred_values = torch.randn(batch_size, 1)\n", "true_values = torch.randn(batch_size, 1)\n", "\n", "# Mean squared error loss\n", "mse_loss = nn.MSELoss()\n", "mse_value = mse_loss(pred_values, true_values)\n", "print(f\"MSE loss: {mse_value:.4f}\")\n", "\n", "# Mean absolute error loss\n", "mae_loss = nn.L1Loss()\n", "mae_value = mae_loss(pred_values, true_values)\n", "print(f\"MAE loss: {mae_value:.4f}\")"]}, {"cell_type": "markdown", "id": "4e5c0fbf", "metadata": {}, "source": ["## Optimizers\n", "\n", "Optimizers in PyTorch are used to **update model parameters** based on gradients computed during backpropagation.\n", "\n", "They are part of `torch.optim` and are essential for training neural networks.\n", "\n", "You must define the optimizer yourself in PyTorch. There is no default — this gives you full flexibility, but also full responsibility.\n", "\n", "### Define the optimizer and pass in the model’s parameters"]}, {"cell_type": "code", "execution_count": 41, "id": "9757a62e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Available optimizers:\n", "- SGD (Stochastic Gradient Descent)\n", "- RMSprop\n", "- Adam (Adaptive Moment Estimation)\n", "- <PERSON><PERSON> (<PERSON> with Weight Decay)\n", "- AdaGrad\n", "\n", "SGD optimizer: SGD (\n", "Parameter Group 0\n", "    dampening: 0\n", "    differentiable: False\n", "    foreach: None\n", "    fused: None\n", "    lr: 0.01\n", "    maximize: False\n", "    momentum: 0.9\n", "    nesterov: <PERSON>alse\n", "    weight_decay: 0\n", ")\n", "RMSprop optimizer: RMSprop (\n", "Parameter Group 0\n", "    alpha: 0.99\n", "    capturable: False\n", "    centered: False\n", "    differentiable: False\n", "    eps: 1e-08\n", "    foreach: None\n", "    lr: 0.01\n", "    maximize: False\n", "    momentum: 0\n", "    weight_decay: 0\n", ")\n", "<PERSON> optimizer: <PERSON> (\n", "Parameter Group 0\n", "    amsgrad: False\n", "    betas: (0.9, 0.999)\n", "    capturable: False\n", "    decoupled_weight_decay: False\n", "    differentiable: False\n", "    eps: 1e-08\n", "    foreach: None\n", "    fused: None\n", "    lr: 0.001\n", "    maximize: False\n", "    weight_decay: 0\n", ")\n", "AdamW optimizer: Adam<PERSON> (\n", "Parameter Group 0\n", "    amsgrad: False\n", "    betas: (0.9, 0.999)\n", "    capturable: False\n", "    decoupled_weight_decay: True\n", "    differentiable: False\n", "    eps: 1e-08\n", "    foreach: None\n", "    fused: None\n", "    lr: 0.001\n", "    maximize: False\n", "    weight_decay: 0.01\n", ")\n"]}], "source": ["import torch.optim as optim\n", "\n", "# Create a simple model\n", "model = nn.Sequential(\n", "    nn.<PERSON><PERSON>(5, 10),\n", "    nn.ReLU(),\n", "    nn.<PERSON><PERSON>(10, 1)\n", ")\n", "\n", "print(\"Available optimizers:\")\n", "print(\"- SGD (Stochastic Gradient Descent)\")\n", "print(\"- RMSprop\")\n", "print(\"- <PERSON> (Adaptive Moment Estimation)\")\n", "print(\"- <PERSON><PERSON> (<PERSON> with Weight Decay)\")\n", "print(\"- Ada<PERSON><PERSON>\")\n", "\n", "# Different optimizers\n", "sgd_optimizer = optim.SGD(model.parameters(), lr=0.01, momentum=0.9)\n", "rmsprop_optimizer = optim.RMSprop(model.parameters(), lr=0.01)\n", "adam_optimizer = optim.<PERSON>(model.parameters(), lr=0.001)\n", "adamw_optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)\n", "\n", "\n", "print(f\"\\nSGD optimizer: {sgd_optimizer}\")\n", "print(f\"RMSprop optimizer: {rmsprop_optimizer}\")\n", "print(f\"Adam optimizer: {adam_optimizer}\")\n", "print(f\"AdamW optimizer: {adamw_optimizer}\")"]}, {"cell_type": "markdown", "id": "018d03e1", "metadata": {}, "source": ["###  Optimizer Step in PyTorch\n", "\n", "After choosing an optimizer, use `optimizer.step()` to **update the model parameters using the gradients computed during backpropagation**.\n", "\n", "This step applies the gradient updates to the model’s weights, moving them in the direction that **minimizes the loss**.\n", "\n", "####  Typical Workflow\n", "```python\n", "loss.backward()       # Compute gradients\n", "optimizer.step()      # Update model parameters\n", "optimizer.zero_grad() # Clear gradients before next step\n", "```\n", "\n", "Below is a simple training example"]}, {"cell_type": "code", "execution_count": 26, "id": "2c6e0d58", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training step:\n", "Initial loss: 0.5564\n", "Loss after one step: 0.5564\n"]}], "source": ["# Dummy training data\n", "x_train = torch.randn(10, 5)\n", "y_train = torch.randn(10, 1)\n", "\n", "# Set up loss and optimizer\n", "criterion = nn.MS<PERSON><PERSON>()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.01)\n", "\n", "print(\"Training step:\")\n", "print(f\"Initial loss: {criterion(model(x_train), y_train).item():.4f}\")\n", "\n", "# One training step\n", "optimizer.zero_grad()    # Clear gradients\n", "outputs = model(x_train) # Forward pass\n", "loss = criterion(outputs, y_train)  # Compute loss\n", "loss.backward()          # Backward pass, compute gradients\n", "optimizer.step()         # Update parameters\n", "\n", "print(f\"Loss after one step: {loss.item():.4f}\")"]}, {"cell_type": "markdown", "id": "d441ba0f", "metadata": {}, "source": ["# Model Inference\n", "\n", "Inference is the process of using a trained model to make predictions on new, unseen data.\n", "Unlike training, inference does not require gradient tracking or parameter updates. \n", "\n", "## Steps for inference\n", "\n", "```python\n", "# 1. Set model to evaluation mode\n", "model.eval()\n", "\n", "# 2. Disable gradient tracking\n", "with torch.no_grad():\n", "    # 3. Forward pass (X could be test data)\n", "    predictions = model(X)\n", "```\n", "\n", "Using the simple training example above, here is the inference code"]}, {"cell_type": "markdown", "id": "435c217b", "metadata": {}, "source": ["# The Complete  Loop\n", "\n", "Putting it all together - a full training example"]}, {"cell_type": "code", "execution_count": 45, "id": "d183ab76", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Step 1: Data Preparation:\n", "Training data: <PERSON><PERSON><PERSON><PERSON>([722, 20])\n", "Validation data: <PERSON><PERSON><PERSON>ze([128, 20])\n", "Test data: <PERSON><PERSON><PERSON><PERSON>([150, 20])\n", "Step 2: Model Definition:\n", "Model:\n", "Sequential(\n", "  (0): Linear(in_features=20, out_features=64, bias=True)\n", "  (1): ReLU()\n", "  (2): Dropout(p=0.2, inplace=False)\n", "  (3): Linear(in_features=64, out_features=32, bias=True)\n", "  (4): ReLU()\n", "  (5): Dropout(p=0.2, inplace=False)\n", "  (6): Linear(in_features=32, out_features=2, bias=True)\n", ")\n", "Total parameters: 3490\n", "Trainable parameters: 3490\n", "Step 3: Training Setup:\n", "Loss function: CrossEntropyLoss()\n", "Optimizer: <PERSON> (\n", "Parameter Group 0\n", "    amsgrad: False\n", "    betas: (0.9, 0.999)\n", "    capturable: False\n", "    decoupled_weight_decay: False\n", "    differentiable: False\n", "    eps: 1e-08\n", "    foreach: None\n", "    fused: None\n", "    lr: 0.001\n", "    maximize: False\n", "    weight_decay: 0\n", ")\n", "Batch size: 32\n", "Step 4: Training Loop:\n", "Epoch [1/10], Train Loss: 0.6603, Val Loss: 0.6226\n", "Epoch [2/10], Train Loss: 0.5872, Val Loss: 0.5201\n", "Epoch [3/10], Train Loss: 0.4660, Val Loss: 0.4263\n", "Epoch [4/10], Train Loss: 0.3958, Val Loss: 0.3879\n", "Epoch [5/10], Train Loss: 0.3635, Val Loss: 0.3790\n", "Epoch [6/10], Train Loss: 0.3340, Val Loss: 0.3767\n", "Epoch [7/10], Train Loss: 0.3378, Val Loss: 0.3774\n", "Epoch [8/10], Train Loss: 0.3070, Val Loss: 0.3811\n", "Epoch [9/10], Train Loss: 0.3038, Val Loss: 0.3836\n", "Epoch [10/10], Train Loss: 0.3073, Val Loss: 0.3777\n", "Epoch [5/10], Train Loss: 0.3635, Val Loss: 0.3790\n", "Epoch [6/10], Train Loss: 0.3340, Val Loss: 0.3767\n", "Epoch [7/10], Train Loss: 0.3378, Val Loss: 0.3774\n", "Epoch [8/10], Train Loss: 0.3070, Val Loss: 0.3811\n", "Epoch [9/10], Train Loss: 0.3038, Val Loss: 0.3836\n", "Epoch [10/10], Train Loss: 0.3073, Val Loss: 0.3777\n"]}], "source": ["print(\"Step 1: Data Preparation:\")\n", "# Generate data\n", "from sklearn.datasets import make_classification\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "import torch\n", "from torch.utils.data import DataLoader, TensorDataset\n", "\n", "X, y = make_classification(n_samples=1000, n_features=20, n_classes=2, random_state=42)\n", "\n", "# First split: train/test (85:15), stratified\n", "X_trainval, X_test, y_trainval, y_test = train_test_split(\n", "    X, y, test_size=0.15, random_state=42, stratify=y\n", ")\n", "\n", "# Second split: train/val (85:15 of trainval), stratified\n", "X_train, X_val, y_train, y_val = train_test_split(\n", "    X_trainval, y_trainval, test_size=0.15, random_state=42, stratify=y_trainval\n", ")\n", "\n", "# Normalize\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_val_scaled = scaler.transform(X_val)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "# Convert to tensors\n", "X_train_tensor = torch.FloatTensor(X_train_scaled)\n", "y_train_tensor = torch.LongTensor(y_train)\n", "X_val_tensor = torch.FloatTensor(X_val_scaled)\n", "y_val_tensor = torch.LongTensor(y_val)\n", "X_test_tensor = torch.FloatTensor(X_test_scaled)\n", "y_test_tensor = torch.LongTensor(y_test)\n", "\n", "print(f\"Training data: {X_train_tensor.shape}\")\n", "print(f\"Validation data: {X_val_tensor.shape}\")\n", "print(f\"Test data: {X_test_tensor.shape}\")\n", "\n", "print(\"Step 2: Model Definition:\")\n", "# Define model\n", "import torch.nn as nn\n", "\n", "model = nn.Sequential(\n", "    nn.<PERSON><PERSON>(20, 64),\n", "    nn.ReLU(),\n", "    nn.Dropout(0.2),\n", "    nn.<PERSON><PERSON>(64, 32),\n", "    nn.ReLU(),\n", "    nn.Dropout(0.2),\n", "    nn.<PERSON><PERSON>(32, 2)\n", ")\n", "\n", "print(f\"Model:\\n{model}\")\n", "\n", "# Count parameters\n", "total_params = sum(p.numel() for p in model.parameters())\n", "trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)\n", "print(f\"Total parameters: {total_params}\")\n", "print(f\"Trainable parameters: {trainable_params}\")\n", "\n", "print(\"Step 3: Training Setup:\")\n", "# Loss and optimizer\n", "import torch.optim as optim\n", "\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.001)\n", "\n", "# Data loaders\n", "train_dataset = TensorDataset(X_train_tensor, y_train_tensor)\n", "val_dataset = TensorDataset(X_val_tensor, y_val_tensor)\n", "train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)\n", "val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)\n", "\n", "print(f\"Loss function: {criterion}\")\n", "print(f\"Optimizer: {optimizer}\")\n", "print(f\"Batch size: 32\")\n", "\n", "print(\"Step 4: Training Loop:\")\n", "import matplotlib.pyplot as plt\n", "\n", "num_epochs = 10\n", "train_losses = []\n", "val_losses = []\n", "\n", "model.train()  # Set model to training mode\n", "\n", "for epoch in range(num_epochs):\n", "    epoch_train_loss = 0.0\n", "    num_train_batches = 0\n", "    \n", "    # Training loop\n", "    for batch_x, batch_y in train_loader:\n", "        outputs = model(batch_x)\n", "        loss = criterion(outputs, batch_y)\n", "        optimizer.zero_grad()\n", "        loss.backward()\n", "        optimizer.step()\n", "        epoch_train_loss += loss.item()\n", "        num_train_batches += 1\n", "    avg_train_loss = epoch_train_loss / num_train_batches\n", "    train_losses.append(avg_train_loss)\n", "\n", "    # Validation loop\n", "    model.eval()\n", "    epoch_val_loss = 0.0\n", "    num_val_batches = 0\n", "    with torch.no_grad():\n", "        for val_x, val_y in val_loader:\n", "            val_outputs = model(val_x)\n", "            val_loss = criterion(val_outputs, val_y)\n", "            epoch_val_loss += val_loss.item()\n", "            num_val_batches += 1\n", "    avg_val_loss = epoch_val_loss / num_val_batches\n", "    val_losses.append(avg_val_loss)\n", "    model.train()\n", "\n", "    print(f\"Epoch [{epoch+1}/{num_epochs}], Train Loss: {avg_train_loss:.4f}, Val Loss: {avg_val_loss:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "ef5eb2d9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Step 6: Visualizing Training:\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(\"Step 5: Visualizing Training:\")\n", "plt.figure(figsize=(12, 5))\n", "\n", "# Plot learning curves\n", "plt.plot(train_losses, label='Train Loss')\n", "plt.plot(val_losses, label='Validation Loss')\n", "plt.title('Learning Curves')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "plt.grid(True)\n"]}, {"cell_type": "code", "execution_count": null, "id": "e00aa58a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Step 5: Evaluation:\n", "Test Loss: 0.2922\n", "Test Accuracy: 0.8933 (134/150)\n"]}], "source": ["print(\"Step 6: Evaluation:\")\n", "model.eval()  # Set model to evaluation mode\n", "\n", "with torch.no_grad():  # Disable gradient computation\n", "    test_outputs = model(X_test_tensor)\n", "    test_loss = criterion(test_outputs, y_test_tensor)\n", "    \n", "    # Calculate accuracy\n", "    _, predicted = torch.max(test_outputs.data, 1)\n", "    correct = (predicted == y_test_tensor).sum().item()\n", "    accuracy = correct / len(y_test_tensor)\n", "    \n", "    print(f\"Test Loss: {test_loss.item():.4f}\")\n", "    print(f\"Test Accuracy: {accuracy:.4f} ({correct}/{len(y_test_tensor)})\")\n"]}, {"cell_type": "code", "execution_count": 50, "id": "50172174", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAk8AAAHWCAYAAABuRm14AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAMydJREFUeJzt3QucTfX+//HP2owxYVxnDJVb7pLLEKKI0SSHGfdKUZTquOTWZc5J0sWI/AmhJJSUKEpCGidOnRGGKSUiSmLcb6NmaGb/H9/vOXt+sxnsxXdm71nzep7HOjN7rTVrffc4He8+n+/6bsvtdrsFAAAAPnH5dhoAAAAUwhMAAIANhCcAAAAbCE8AAAA2EJ4AAABsIDwBAADYQHgCAACwgfAEAABgA+EJAADABsIT4CA7d+6UO+64Q0qWLCmWZcnSpUuNXv+XX37R1507d67R6+Znbdq00RuAgoPwBBj2888/yyOPPCLVqlWTokWLSmhoqLRs2VJeffVV+fPPP3P13n379pWtW7fKSy+9JO+88440adJEnOKBBx7QwU39PnP6PargqI6r7ZVXXrF9/f3798tzzz0nycnJhkYMwKkK+3sAgJMsX75cevToIcHBwdKnTx+58cYb5ezZs/LVV1/JE088IT/88IO88cYbuXJvFSgSExPln//8pwwaNChX7lG5cmV9n6CgIPGHwoULyx9//CHLli2Tnj17eh179913dVhNS0u7omur8DRmzBipUqWKNGzY0Oef+/zzz6/ofgDyL8ITYMiePXvk7rvv1gFjzZo1UqFChaxjAwcOlF27dulwlVsOHz6sv5YqVSrX7qGqOiqg+IsKpaqK9957710QnhYsWCAdO3aUDz/8ME/GokLcNddcI0WKFMmT+wEIHLTtAEPGjx8vqampMnv2bK/g5FG9enV5/PHHs17/9ddf8sILL8gNN9ygQ4GqePzjH/+Q9PR0r59T+//2t7/p6tXNN9+sw4tqCb799ttZ56h2kwptiqpwqZCjfs7T7vJ8n536GXVedqtXr5ZWrVrpAFa8eHGpVauWHtPl5jypsHjrrbdKsWLF9M/GxMTIjz/+mOP9VIhUY1LnqblZDz74oA4ivrr33ntlxYoVcuLEiax9Gzdu1G07dex8x44dk5EjR0r9+vX1e1Jtvw4dOsi3336bdc6XX34pTZs21d+r8Xjaf573qeY0qSpiUlKS3HbbbTo0eX4v5895Uq1T9Wd0/vuPjo6W0qVL6woXgPyN8AQYolpJKtTccsstPp3/0EMPybPPPiuNGzeWSZMmSevWrSU+Pl5Xr86nAkf37t2lffv2MnHiRP2XsAogqg2odO3aVV9Dueeee/R8p8mTJ9sav7qWCmkqvD3//PP6Pp07d5avv/76kj/3xRdf6GBw6NAhHZCGDx8u//nPf3SFSIWt86mK0enTp/V7Vd+rgKLaZb5S71UFm48++sir6lS7dm39uzzf7t279cR59d7+3//7fzpcqnlh6vftCTJ16tTR71kZMGCA/v2pTQUlj6NHj+rQpVp66nd7++235zg+NbctLCxMh6iMjAy97/XXX9ftvalTp0rFihV9fq8AApQbwFU7efKkW/3jFBMT49P5ycnJ+vyHHnrIa//IkSP1/jVr1mTtq1y5st63bt26rH2HDh1yBwcHu0eMGJG1b8+ePfq8CRMmeF2zb9+++hrnGz16tD7fY9KkSfr14cOHLzpuzz3mzJmTta9hw4bu8PBw99GjR7P2ffvtt26Xy+Xu06fPBffr16+f1zW7dOniLlu27EXvmf19FCtWTH/fvXt3d7t27fT3GRkZ7oiICPeYMWNy/B2kpaXpc85/H+r39/zzz2ft27hx4wXvzaN169b62MyZM3M8prbsVq1apc9/8cUX3bt373YXL17cHRsbe9n3CCB/oPIEGHDq1Cn9tUSJEj6d/9lnn+mvqkqT3YgRI/TX8+dG1a1bV7fFPFRlQ7XUVFXFFM9cqY8//lgyMzN9+pkDBw7op9NUFaxMmTJZ+2+66SZdJfO8z+weffRRr9fqfamqjud36AvVnlOttpSUFN0yVF9zatkpqiXqcv33/+pUJUjdy9OS3Lx5s8/3VNdRLT1fqOUi1BOXqpqlKmWqjaeqTwCcgfAEGKDm0SiqHeWLX3/9Vf+********************************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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Confusion matrix visualization\n", "from sklearn.metrics import confusion_matrix\n", "import seaborn as sns\n", "\n", "cm = confusion_matrix(y_test_tensor.numpy(), predicted.numpy())\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')\n", "plt.title('Confusion Matrix')\n", "plt.xlabel('Predicted')\n", "plt.ylabel('Actual')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "06e3d43b", "metadata": {}, "source": ["# Model Saving and Loading\n", "\n", "Saving a model lets you reuse it later — for inference, continuing training, or deployment — without retraining.\n", "\n", "PyTorch provides flexible tools to save and load models.\n", "\n", "## Saving Models in PyTorch\n", "\n", "### Saving Models (Save the Model’s State Dictionary)"]}, {"cell_type": "code", "execution_count": null, "id": "6ad07820", "metadata": {}, "outputs": [], "source": ["torch.save(model.state_dict(), 'model_weights.pth')"]}, {"cell_type": "markdown", "id": "cdf21e36", "metadata": {}, "source": ["- Saves only the model’s **learned parameters** (not architecture)  \n", "- You must reconstruct the model when loading  \n", "- ✅ Best practice for **inference**, **deployment**, and **cross-version safety**  \n", "\n", "📁 Saved file: `'model_weights.pth'`\n"]}, {"cell_type": "markdown", "id": "67ded519", "metadata": {}, "source": ["### Save a Training Checkpoint (for Resuming Training)"]}, {"cell_type": "code", "execution_count": null, "id": "0c879bf7", "metadata": {}, "outputs": [], "source": ["checkpoint = {\n", "    'epoch': num_epochs,\n", "    'model_state_dict': model.state_dict(),\n", "    'optimizer_state_dict': optimizer.state_dict(),\n", "    'loss': train_losses[-1],\n", "}\n", "torch.save(checkpoint, 'checkpoint.pth')"]}, {"cell_type": "markdown", "id": "2ae39c56", "metadata": {}, "source": ["- Saves everything needed to **resume training**  \n", "- Includes:\n", "  - Current epoch  \n", "  - Model parameters  \n", "  - Optimizer state  \n", "  - Last recorded loss  \n", "- ✅ Use this if you need to pause and resume training later  \n", "\n", "📁 Saved file: `'checkpoint.pth'`\n"]}, {"cell_type": "markdown", "id": "3d2ab320", "metadata": {}, "source": ["## Step 2: Loading Models\n", "\n", "### Load the state dict"]}, {"cell_type": "code", "execution_count": null, "id": "db09ba37", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["step 2: Loading Models:\n", "✓ Loaded complete model\n", "✓ Loaded model weights into new model\n", "✓ Loaded checkpoint from epoch 10 with loss 0.2960\n"]}], "source": ["# Load state dict (need to create model first)\n", "new_model = nn.Sequential(\n", "    nn.<PERSON><PERSON>(20, 64),\n", "    nn.ReLU(),\n", "    nn.Dropout(0.2),\n", "    nn.<PERSON><PERSON>(64, 32),\n", "    nn.ReLU(),\n", "    nn.Dropout(0.2),\n", "    nn.<PERSON><PERSON>(32, 2)\n", ")\n", "new_model.load_state_dict(torch.load('model_weights.pth'))\n", "print(\"✓ Loaded model weights into new model\")"]}, {"cell_type": "markdown", "id": "78f11d7d", "metadata": {}, "source": ["### Load the Checkpoint"]}, {"cell_type": "code", "execution_count": null, "id": "26a3b2be", "metadata": {}, "outputs": [], "source": ["# Load checkpoint\n", "checkpoint = torch.load('checkpoint.pth')\n", "new_model.load_state_dict(checkpoint['model_state_dict'])\n", "optimizer.load_state_dict(checkpoint['optimizer_state_dict'])\n", "epoch = checkpoint['epoch']\n", "loss = checkpoint['loss']\n", "print(f\"✓ Loaded checkpoint from epoch {epoch} with loss {loss:.4f}\")"]}, {"cell_type": "markdown", "id": "54f14e36", "metadata": {}, "source": ["##  Step 3: Inference with Loaded Model"]}, {"cell_type": "code", "execution_count": 31, "id": "9d4aabb2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Outputs match: True\n", "Original predictions: tensor([1, 1, 1, 1, 1])\n", "Loaded predictions: tensor([1, 1, 1, 1, 1])\n"]}], "source": ["# Step 3: Inference with Loaded Model\n", "# Test that loaded model works\n", "new_model.eval()\n", "with torch.no_grad():\n", "    sample_input = X_test_tensor[:5]  # First 5 test samples\n", "    original_output = model(sample_input)\n", "    loaded_output = new_model(sample_input)\n", "    \n", "    print(\"Outputs match:\", torch.allclose(original_output, loaded_output))\n", "    print(f\"Original predictions: {torch.argmax(original_output, dim=1)}\")\n", "    print(f\"Loaded predictions: {torch.argmax(loaded_output, dim=1)}\")"]}, {"cell_type": "markdown", "id": "061f9a42", "metadata": {}, "source": ["# GPU Usage\n", "\n", "PyTorch can train large models **much faster on a GPU than on a CPU**.\n", "However, you must **explicitly move** both your model and data to the GPU device.\n", "\n", "## Step 1: Check for GPU Availability"]}, {"cell_type": "code", "execution_count": 32, "id": "c4a064ce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Step 1: Checking GPU Availability:\n", "CUDA available: False\n", "CUDA not available - using CPU\n"]}], "source": ["print(\"Step 1: Checking GPU Availability:\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "if torch.cuda.is_available():\n", "    print(f\"CUDA device count: {torch.cuda.device_count()}\")\n", "    print(f\"Current CUDA device: {torch.cuda.current_device()}\")\n", "    print(f\"CUDA device name: {torch.cuda.get_device_name(0)}\")\n", "else:\n", "    print(\"CUDA not available - using CPU\")"]}, {"cell_type": "markdown", "id": "9313d331", "metadata": {}, "source": ["## Step 2:  Device Configuration"]}, {"cell_type": "code", "execution_count": 33, "id": "f581d6f7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cpu\n", "Using CPU\n"]}], "source": ["# Set device\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")\n", "\n", "# Alternative way to check and set device\n", "if torch.cuda.is_available():\n", "    device = torch.device('cuda')\n", "    print(\"Using GPU\")\n", "else:\n", "    device = torch.device('cpu')\n", "    print(\"Using CPU\")"]}, {"cell_type": "markdown", "id": "20c67380", "metadata": {}, "source": ["## Step 3: Moving Tensors and Models to GPU:"]}, {"cell_type": "code", "execution_count": 34, "id": "3b28f7f1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["step 3: Moving Tensors and Models to GPU:\n", "Original tensor device: cpu\n", "Tensor moved to: cpu\n", "Model moved to: cpu\n"]}], "source": ["print(\"step 3: Moving Tensors and Models to GPU:\")\n", "# Move tensors to device\n", "sample_tensor = torch.randn(3, 4)\n", "print(f\"Original tensor device: {sample_tensor.device}\")\n", "\n", "sample_tensor = sample_tensor.to(device)\n", "print(f\"Tensor moved to: {sample_tensor.device}\")\n", "\n", "# Move model to device\n", "model = model.to(device)\n", "print(f\"Model moved to: {next(model.parameters()).device}\")"]}, {"cell_type": "markdown", "id": "94e86112", "metadata": {}, "source": ["## Step 4: Common Training Loop with GPU"]}, {"cell_type": "code", "execution_count": 35, "id": "90ea529d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["step 4:  GPU Training Template:\n", "Template for GPU training:\n", "\n", "# Setup\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "model = model.to(device)\n", "\n", "# In training loop:\n", "for batch_x, batch_y in train_loader:\n", "    # Move data to device\n", "    batch_x = batch_x.to(device)\n", "    batch_y = batch_y.to(device)\n", "\n", "    # Forward pass\n", "    outputs = model(batch_x)\n", "    loss = criterion(outputs, batch_y)\n", "\n", "    # Backward pass\n", "    optimizer.zero_grad()\n", "    loss.backward()\n", "    optimizer.step()\n", "\n"]}], "source": ["print(\"step 4:  GPU Training Template:\")\n", "print(\"Template for GPU training:\")\n", "print(\"\"\"\n", "# Setup\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "model = model.to(device)\n", "\n", "# In training loop:\n", "for batch_x, batch_y in train_loader:\n", "    # Move data to device\n", "    batch_x = batch_x.to(device)\n", "    batch_y = batch_y.to(device)\n", "    \n", "    # Forward pass\n", "    outputs = model(batch_x)\n", "    loss = criterion(outputs, batch_y)\n", "    \n", "    # Backward pass\n", "    optimizer.zero_grad()\n", "    loss.backward()\n", "    optimizer.step()\n", "\"\"\")"]}, {"cell_type": "markdown", "id": "ae5301cd", "metadata": {}, "source": ["# Advanced Training Techniques in PyTorch\n", "\n", "## Learning Rate Scheduling\n", "\n", "### Learning rate affects convergence speed and stability\n", "\n", "* A high learning rate might cause the model to overshoot the minimum and never converge.\n", "* A low learning rate may result in slow learning or getting stuck in poor local minima.\n", "\n", "### Different training phases need different step sizes\n", "\n", "* In the early epochs, a **larger learning rate** helps make faster progress.\n", "\n", "* In the later epochs, a **smaller learning rate** allows fine-tuning around the minimum."]}, {"cell_type": "markdown", "id": "3da5a530", "metadata": {}, "source": ["\n", "\n", "A **learning rate scheduler** dynamically adjusts the learning rate during training. This helps models converge faster and generalize better"]}, {"cell_type": "markdown", "id": "4993b2bd", "metadata": {}, "source": ["`ReduceLROnPlateau` is a learning rate scheduler in PyTorch that reduces the learning rate when a monitored metric has stopped improving on validation set. This adaptive approach helps models escape local minima and achieve better convergence.\n", "\n", "Example: \n", "```python\n", "scheduler = ReduceLROnPlateau(optimizer, mode='min', patience=3, factor=0.5)\n", "\n", "for epoch in range(num_epochs):\n", "    train(...)\n", "    val_loss = validate(...)\n", "    scheduler.step(val_loss)\n", "```\n", "\n", "\n", "### Commom Schedulers\n", "\n", "| Scheduler                  | Description                                  |\n", "|---------------------------|----------------------------------------------|\n", "| `StepLR`                  | Decreases LR by a factor every N epochs      |\n", "| `ExponentialLR`           | Decays LR exponentially                      |\n", "| `ReduceLROnPlateau`       | Reduces LR when a metric has stopped improving |\n", "| `CosineAnnealingLR`       | Gradually lowers LR using cosine function     |\n", "| `OneCycleLR`              | Popular for fast convergence and stable training |"]}, {"cell_type": "markdown", "id": "3c42914b", "metadata": {}, "source": ["##  Advanced Optimizers: AdamW\n", "\n", "`AdamW` (Adam with decoupled Weight Decay) is an improved version of the standard Adam optimizer.  \n", "It was introduced to **fix a flaw in how <PERSON> handles weight decay**, leading to better generalization and training stability.\n", "\n", "\n", "### Why Use AdamW?\n", "\n", "- In regular `Adam`, weight decay is applied as L2 penalty on the gradients, which interferes with <PERSON>'s adaptive learning rate.\n", "- `AdamW` **decouples weight decay** from the optimizer step and applies it **directly to the weights**, resulting in:\n", "  - Better regularization\n", "  - More stable and predictable convergence\n", "  - Superior performance in many modern models\n", "\n", "\n", "### When to Use AdamW\n", "\n", "- When using **weight decay** as a regularizer\n", "- When training **deep models**, including:\n", "  - Transformer-based NLP models\n", "  - ResNet and CNN architectures\n", "- When combining with **learning rate schedulers** like `CosineAnnealingLR` or `OneCycleLR`\n", "\n", "\n", "### How to Use in PyTorch\n", "\n", "```python\n", "from torch.optim import AdamW\n", "\n", "optimizer = AdamW(model.parameters(), lr=1e-3, weight_decay=1e-2)\n", "```\n", "\n", "AdamW was introduced as part of a research improvement [(Loshchilov & Hutter, 2019)](https://arxiv.org/abs/1711.05101)"]}, {"cell_type": "markdown", "id": "bdaab14c", "metadata": {}, "source": ["## Combine Learning rate decay, weight decay, and `AdamW`\n", "\n", "```python\n", "optimizer = torch.optim.AdamW(model.parameters(), lr=0.01, weight_decay=1e-4)\n", "scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=20)\n", "\n", "for epoch in range(num_epochs):\n", "    train(...)\n", "    scheduler.step()\n", "```\n", "\n", "✅ These tools are easy to integrate and can significantly boost model performance."]}, {"cell_type": "markdown", "id": "e0f902a5", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}