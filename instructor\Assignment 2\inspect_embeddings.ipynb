{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Assignment 2: Inspecting and Comparing Word Embeddings\n", "\n", "This notebook compares four different types of word embeddings:\n", "1. **Our PyTorch Word2Vec** - Custom implementation with negative sampling\n", "2. **Gensim Word2Vec** - Trained on text8 corpus\n", "3. **Pretrained Google News** - Word2Vec trained on Google News corpus\n", "4. **Random Baseline** - Random vectors for comparison\n", "\n", "We'll evaluate them using sentence similarity tasks."]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "title: \"Assignment 2: Implement and Compare Word Embeddings\"\n", "author: \"<PERSON><PERSON>\"\n", "format: \n", "  html:\n", "    toc: true\n", "    toc-title: Contents\n", "    toc-depth: 4\n", "    self-contained: true\n", "    number-sections: false\n", "jupyter: python3\n", "---"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries loaded successfully!\n"]}], "source": ["import pickle\n", "import numpy as np\n", "import pandas as pd\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "from gensim.models import Word2Vec, KeyedVectors\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"Libraries loaded successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load All Embedding Types"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PyTorch Word2Vec: 253854 words, 50 dimensions\n"]}], "source": ["# Load PyTorch Word2Vec embeddings\n", "with open('test_word2vec_embeddings.pkl', 'rb') as f:\n", "    pytorch_data = pickle.load(f)\n", "\n", "pytorch_embeddings = pytorch_data['embeddings']\n", "pytorch_word2idx = pytorch_data['word2idx']\n", "pytorch_idx2word = pytorch_data['idx2word']\n", "\n", "print(f\"PyTorch Word2Vec: {pytorch_embeddings.shape[0]} words, {pytorch_embeddings.shape[1]} dimensions\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Gensim Word2Vec: 18497 words, 100 dimensions\n"]}], "source": ["# Load Gensim Word2Vec embeddings\n", "with open('word2vec_gensim_embeddings.pkl', 'rb') as f:\n", "    gensim_data = pickle.load(f)\n", "\n", "gensim_embeddings = gensim_data['embeddings']\n", "gensim_word2idx = gensim_data['word2idx']\n", "gensim_idx2word = gensim_data['idx2word']\n", "\n", "print(f\"Gensim Word2Vec: {gensim_embeddings.shape[0]} words, {gensim_embeddings.shape[1]} dimensions\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Google News Word2Vec: 3000000 words, 300 dimensions\n"]}], "source": ["# Load Pretrained Google News Word2Vec\n", "try:\n", "    google_model = KeyedVectors.load('word2vec-google-news-300.model')\n", "    print(f\"Google News Word2Vec: {len(google_model.key_to_index)} words, {google_model.vector_size} dimensions\")\n", "    google_available = True\n", "except Exception as e:\n", "    print(f\"Could not load Google News model: {e}\")\n", "    google_available = False"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Helper Functions for Sentence Embeddings"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Helper functions defined!\n"]}], "source": ["def get_sentence_embedding_pytorch(sentence, embeddings, word2idx, embedding_dim=50):\n", "    \"\"\"Compute sentence embedding by averaging word embeddings (PyTorch).\"\"\"\n", "    words = sentence.lower().split()\n", "    word_vectors = []\n", "    \n", "    for word in words:\n", "        if word in word2idx:\n", "            idx = word2idx[word]\n", "            word_vectors.append(embeddings[idx])\n", "    \n", "    if word_vectors:\n", "        return np.mean(word_vectors, axis=0)\n", "    else:\n", "        return np.zeros(embedding_dim)\n", "\n", "def get_sentence_embedding_gensim(sentence, embeddings, word2idx, embedding_dim=100):\n", "    \"\"\"Compute sentence embedding by averaging word embeddings (Gensim).\"\"\"\n", "    words = sentence.lower().split()\n", "    word_vectors = []\n", "    \n", "    for word in words:\n", "        if word in word2idx:\n", "            idx = word2idx[word]\n", "            word_vectors.append(embeddings[idx])\n", "    \n", "    if word_vectors:\n", "        return np.mean(word_vectors, axis=0)\n", "    else:\n", "        return np.zeros(embedding_dim)\n", "\n", "def get_sentence_embedding_google(sentence, model):\n", "    \"\"\"Compute sentence embedding using Google News Word2Vec.\"\"\"\n", "    words = sentence.lower().split()\n", "    word_vectors = []\n", "    \n", "    for word in words:\n", "        if word in model.key_to_index:\n", "            word_vectors.append(model[word])\n", "    \n", "    if word_vectors:\n", "        return np.mean(word_vectors, axis=0)\n", "    else:\n", "        return np.zeros(model.vector_size)\n", "\n", "def get_sentence_embedding_random(sentence, embedding_dim=100):\n", "    \"\"\"Generate random sentence embedding for baseline comparison.\"\"\"\n", "    np.random.seed(hash(sentence) % 2**32)  # Consistent random for same sentence\n", "    return np.random.normal(0, 1, embedding_dim)\n", "\n", "print(\"Helper functions defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Define Test Sentences for Similarity Comparison"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: 'The cat sat on the mat'\n", "\n", "Comparing against 8 sentences:\n", "1. A cat is sitting on a rug\n", "2. The dog lay on the floor\n", "3. Animals like to rest comfortably\n", "4. The weather is nice today\n", "5. Mathematics is a difficult subject\n", "6. Cats are furry pets\n", "7. The mat was very soft\n", "8. Sitting is comfortable\n"]}], "source": ["# Query sentence\n", "query_sentence = \"The cat sat on the mat\"\n", "\n", "# Comparison sentences with expected similarity rankings\n", "comparison_sentences = [\n", "    \"A cat is sitting on a rug\",           # High similarity (same meaning)\n", "    \"The dog lay on the floor\",            # Medium similarity (similar structure, different animals)\n", "    \"Animals like to rest comfortably\",    # Medium similarity (related concept)\n", "    \"The weather is nice today\",           # Low similarity (unrelated)\n", "    \"Mathematics is a difficult subject\",   # Low similarity (completely unrelated)\n", "    \"Cats are furry pets\",                 # Medium similarity (mentions cats)\n", "    \"The mat was very soft\",               # Medium similarity (mentions mat)\n", "    \"Sitting is comfortable\",              # Low-medium similarity (mentions sitting)\n", "]\n", "\n", "print(f\"Query: '{query_sentence}'\")\n", "print(f\"\\nComparing against {len(comparison_sentences)} sentences:\")\n", "for i, sent in enumerate(comparison_sentences, 1):\n", "    print(f\"{i}. {sent}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Compute Sentence Similarities for All Embedding Types"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query embeddings computed for all models!\n"]}], "source": ["# Compute query embedding for each model\n", "query_pytorch = get_sentence_embedding_pytorch(query_sentence, pytorch_embeddings, pytorch_word2idx, 50)\n", "query_gensim = get_sentence_embedding_gensim(query_sentence, gensim_embeddings, gensim_word2idx, 100)\n", "query_random = get_sentence_embedding_random(query_sentence, 100)\n", "\n", "if google_available:\n", "    query_google = get_sentence_embedding_google(query_sentence, google_model)\n", "\n", "print(\"Query embeddings computed for all models!\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Similarities computed for all sentences!\n"]}], "source": ["# Compute similarities for all comparison sentences\n", "results = []\n", "\n", "for i, sentence in enumerate(comparison_sentences):\n", "    # Get embeddings for this sentence\n", "    sent_pytorch = get_sentence_embedding_pytorch(sentence, pytorch_embeddings, pytorch_word2idx, 50)\n", "    sent_gensim = get_sentence_embedding_gensim(sentence, gensim_embeddings, gensim_word2idx, 100)\n", "    sent_random = get_sentence_embedding_random(sentence, 100)\n", "    \n", "    # Compute cosine similarities\n", "    sim_pytorch = cosine_similarity([query_pytorch], [sent_pytorch])[0][0]\n", "    sim_gensim = cosine_similarity([query_gensim], [sent_gensim])[0][0]\n", "    sim_random = cosine_similarity([query_random], [sent_random])[0][0]\n", "    \n", "    result = {\n", "        'sentence': sentence,\n", "        'pytorch_sim': sim_pytorch,\n", "        'gensim_sim': sim_gensim,\n", "        'random_sim': sim_random\n", "    }\n", "    \n", "    if google_available:\n", "        sent_google = get_sentence_embedding_google(sentence, google_model)\n", "        sim_google = cosine_similarity([query_google], [sent_google])[0][0]\n", "        result['google_sim'] = sim_google\n", "    \n", "    results.append(result)\n", "\n", "# Create DataFrame for easy analysis\n", "df_results = pd.DataFrame(results)\n", "print(\"Similarities computed for all sentences!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> Results"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "SENTENCE SIMILARITY COMPARISON\n", "Query: 'The cat sat on the mat'\n", "================================================================================\n", "                          sentence  pytorch_sim  gensim_sim  random_sim  google_sim\n", "         A cat is sitting on a rug       0.9594      0.6055     -0.0986      0.7552\n", "          The dog lay on the floor       0.9996      0.9040      0.1162      0.7736\n", "  Animals like to rest comfortably       0.9326      0.2027      0.0610      0.4742\n", "         The weather is nice today       0.9911      0.2260     -0.0124      0.3829\n", "Mathematics is a difficult subject       0.9563      0.1210     -0.0875      0.2272\n", "               Cats are furry pets       0.7830     -0.0988      0.1524      0.4243\n", "             The mat was very soft       0.9891      0.2418      0.0314      0.6417\n", "            Sitting is comfortable       0.8638      0.1662      0.0911      0.5023\n"]}], "source": ["# Display results table\n", "print(f\"\\n{'='*80}\")\n", "print(f\"SENTENCE SIMILARITY COMPARISON\")\n", "print(f\"Query: '{query_sentence}'\")\n", "print(f\"{'='*80}\")\n", "\n", "# Format and display results\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 50)\n", "\n", "# Round similarities for better display\n", "display_df = df_results.copy()\n", "for col in ['pytorch_sim', 'gensim_sim', 'random_sim']:\n", "    if col in display_df.columns:\n", "        display_df[col] = display_df[col].round(4)\n", "if 'google_sim' in display_df.columns:\n", "    display_df['google_sim'] = display_df['google_sim'].round(4)\n", "\n", "print(display_df.to_string(index=False))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "RANKING BY SIMILARITY (Highest to Lowest)\n", "================================================================================\n", "\n", "PYTORCH WORD2VEC RANKING:\n", "1. The dog lay on the floor                                     (sim: 0.9996)\n", "2. The weather is nice today                                    (sim: 0.9911)\n", "3. The mat was very soft                                        (sim: 0.9891)\n", "4. A cat is sitting on a rug                                    (sim: 0.9594)\n", "5. Mathematics is a difficult subject                           (sim: 0.9563)\n", "6. Animals like to rest comfortably                             (sim: 0.9326)\n", "7. Sitting is comfortable                                       (sim: 0.8638)\n", "8. Cats are furry pets                                          (sim: 0.7830)\n", "\n", "GENSIM WORD2VEC RANKING:\n", "1. The dog lay on the floor                                     (sim: 0.9040)\n", "2. A cat is sitting on a rug                                    (sim: 0.6055)\n", "3. The mat was very soft                                        (sim: 0.2418)\n", "4. The weather is nice today                                    (sim: 0.2260)\n", "5. Animals like to rest comfortably                             (sim: 0.2027)\n", "6. Sitting is comfortable                                       (sim: 0.1662)\n", "7. Mathematics is a difficult subject                           (sim: 0.1210)\n", "8. Cats are furry pets                                          (sim: -0.0988)\n", "\n", "RANDOM WORD2VEC RANKING:\n", "1. Cats are furry pets                                          (sim: 0.1524)\n", "2. The dog lay on the floor                                     (sim: 0.1162)\n", "3. Sitting is comfortable                                       (sim: 0.0911)\n", "4. Animals like to rest comfortably                             (sim: 0.0610)\n", "5. The mat was very soft                                        (sim: 0.0314)\n", "6. The weather is nice today                                    (sim: -0.0124)\n", "7. Mathematics is a difficult subject                           (sim: -0.0875)\n", "8. A cat is sitting on a rug                                    (sim: -0.0986)\n", "\n", "GOOGLE WORD2VEC RANKING:\n", "1. The dog lay on the floor                                     (sim: 0.7736)\n", "2. A cat is sitting on a rug                                    (sim: 0.7552)\n", "3. The mat was very soft                                        (sim: 0.6417)\n", "4. Sitting is comfortable                                       (sim: 0.5023)\n", "5. Animals like to rest comfortably                             (sim: 0.4742)\n", "6. Cats are furry pets                                          (sim: 0.4243)\n", "7. The weather is nice today                                    (sim: 0.3829)\n", "8. Mathematics is a difficult subject                           (sim: 0.2272)\n"]}], "source": ["# Rank sentences by similarity for each embedding type\n", "print(f\"\\n{'='*80}\")\n", "print(\"RANKING BY SIMILARITY (Highest to Lowest)\")\n", "print(f\"{'='*80}\")\n", "\n", "embedding_types = ['pytorch_sim', 'gensim_sim', 'random_sim']\n", "if google_available:\n", "    embedding_types.append('google_sim')\n", "\n", "for emb_type in embedding_types:\n", "    print(f\"\\n{emb_type.replace('_sim', '').upper()} WORD2VEC RANKING:\")\n", "    ranked = df_results.sort_values(emb_type, ascending=False)\n", "    for i, (_, row) in enumerate(ranked.iterrows(), 1):\n", "        print(f\"{i}. {row['sentence'][:60]:<60} (sim: {row[emb_type]:.4f})\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Analysis and Comparison"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "ANALYSIS: How Well Do Embeddings Capture Semantic Similarity?\n", "================================================================================\n", "\n", "PYTORCH Analysis:\n", "Top 3 most similar: ['The dog lay on the floor', 'The weather is nice today', 'The mat was very soft']\n", "Expected high-similarity sentences in top 3: 1/2\n", "Rank of most semantically similar sentence: #1\n", "\n", "GENSIM Analysis:\n", "Top 3 most similar: ['The dog lay on the floor', 'A cat is sitting on a rug', 'The mat was very soft']\n", "Expected high-similarity sentences in top 3: 2/2\n", "Rank of most semantically similar sentence: #1\n", "\n", "RANDOM Analysis:\n", "Top 3 most similar: ['Cats are furry pets', 'The dog lay on the floor', 'Sitting is comfortable']\n", "Expected high-similarity sentences in top 3: 1/2\n", "Rank of most semantically similar sentence: #1\n", "\n", "GOOGLE Analysis:\n", "Top 3 most similar: ['The dog lay on the floor', 'A cat is sitting on a rug', 'The mat was very soft']\n", "Expected high-similarity sentences in top 3: 2/2\n", "Rank of most semantically similar sentence: #1\n"]}], "source": ["# Analyze which sentences should be most similar (ground truth)\n", "expected_high_similarity = [\n", "    \"A cat is sitting on a rug\",  # Should be #1 - same meaning\n", "    \"The dog lay on the floor\",   # Should be #2 - similar structure\n", "]\n", "\n", "expected_medium_similarity = [\n", "    \"Animals like to rest comfortably\",\n", "    \"Cats are furry pets\",\n", "    \"The mat was very soft\",\n", "]\n", "\n", "expected_low_similarity = [\n", "    \"The weather is nice today\",\n", "    \"Mathematics is a difficult subject\",\n", "]\n", "\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"ANALYSIS: How Well Do Embeddings Capture Semantic Similarity?\")\n", "print(\"=\"*80)\n", "\n", "def analyze_ranking(emb_type, df):\n", "    ranked = df.sort_values(emb_type, ascending=False)\n", "    top_sentences = ranked['sentence'].head(3).tolist()\n", "    \n", "    print(f\"\\n{emb_type.replace('_sim', '').upper()} Analysis:\")\n", "    print(f\"Top 3 most similar: {top_sentences}\")\n", "    \n", "    # Check if expected high similarity sentences are in top 3\n", "    high_sim_in_top3 = sum(1 for sent in expected_high_similarity if sent in top_sentences)\n", "    print(f\"Expected high-similarity sentences in top 3: {high_sim_in_top3}/2\")\n", "    \n", "    # Check ranking of the most similar sentence\n", "    most_similar_expected = \"A cat is sitting on a rug\"\n", "    rank = ranked[ranked['sentence'] == most_similar_expected].index[0] + 1\n", "    print(f\"Rank of most semantically similar sentence: #{rank}\")\n", "    \n", "    return high_sim_in_top3, rank\n", "\n", "# Analyze each embedding type\n", "analysis_results = {}\n", "for emb_type in embedding_types:\n", "    high_score, rank = analyze_ranking(emb_type, df_results)\n", "    analysis_results[emb_type] = {'high_score': high_score, 'best_rank': rank}"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "FINAL COMPARISON SUMMARY\n", "================================================================================\n", "\n", "Performance Ranking (Best to Worst):\n", "1. GENSIM          (Score: 5/5, Top sentences: 2/2, Best rank: #1)\n", "2. GOOGLE          (Score: 5/5, Top sentences: 2/2, Best rank: #1)\n", "3. PYTORCH         (Score: 4/5, Top sentences: 1/2, Best rank: #1)\n", "4. RAND<PERSON>          (Score: 4/5, Top sentences: 1/2, Best rank: #1)\n", "\n", "Key Insights:\n", "• Higher scores indicate better semantic understanding\n", "• Random baseline should perform worst (as expected)\n", "• Pretrained models typically outperform custom models due to larger training data\n", "• Gensim and PyTorch models show how implementation affects results\n"]}], "source": ["# Summary comparison\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"FINAL COMPARISON SUMMARY\")\n", "print(\"=\"*80)\n", "\n", "print(\"\\nPerformance Ranking (Best to Worst):\")\n", "performance_scores = []\n", "for emb_type, results in analysis_results.items():\n", "    # Simple scoring: high_score (0-2) + bonus for rank 1 (3 points), rank 2 (2 points), rank 3 (1 point)\n", "    rank_bonus = max(0, 4 - results['best_rank'])\n", "    total_score = results['high_score'] + rank_bonus\n", "    performance_scores.append((emb_type, total_score, results))\n", "\n", "performance_scores.sort(key=lambda x: x[1], reverse=True)\n", "\n", "for i, (emb_type, score, results) in enumerate(performance_scores, 1):\n", "    name = emb_type.replace('_sim', '').upper()\n", "    print(f\"{i}. {name:<15} (Score: {score}/5, Top sentences: {results['high_score']}/2, Best rank: #{results['best_rank']})\")\n", "\n", "print(\"\\nKey Insights:\")\n", "print(\"• Higher scores indicate better semantic understanding\")\n", "print(\"• Random baseline should perform worst (as expected)\")\n", "print(\"• Pretrained models typically outperform custom models due to larger training data\")\n", "print(\"• Gensim and PyTorch models show how implementation affects results\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Clustering Visualizations (PCA and t-SNE)\n", "\n", "Let's visualize how different embedding types cluster words in 2D space using dimensionality reduction techniques."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Clustering libraries imported!\n"]}], "source": ["# Import additional libraries for clustering visualization\n", "from sklearn.decomposition import PCA\n", "from sklearn.manifold import TSNE\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "print(\"Clustering libraries imported!\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Selected 24 words for clustering visualization\n", "Words: ['cat', 'dog', 'animal', 'pet', 'mat', 'rug', 'floor', 'chair', 'sit', 'sitting', 'lay', 'rest', 'weather', 'nice', 'today', 'sun', 'mathematics', 'difficult', 'subject', 'study', 'comfortable', 'soft', 'furry', 'good']\n"]}], "source": ["# Select sample words for visualization\n", "sample_words = [\n", "    # Animals\n", "    'cat', 'dog', 'animal', 'pet',\n", "    # Furniture/Objects\n", "    'mat', 'rug', 'floor', 'chair',\n", "    # Actions\n", "    'sit', 'sitting', 'lay', 'rest',\n", "    # Weather/Nature\n", "    'weather', 'nice', 'today', 'sun',\n", "    # Academic\n", "    'mathematics', 'difficult', 'subject', 'study',\n", "    # Descriptive\n", "    'comfortable', 'soft', 'furry', 'good'\n", "]\n", "\n", "print(f\"Selected {len(sample_words)} words for clustering visualization\")\n", "print(f\"Words: {sample_words}\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PyTorch: 24 words found\n", "Gensim: 22 words found\n", "Google: 24 words found\n"]}], "source": ["def get_word_embeddings_for_visualization(words, embedding_type):\n", "    \"\"\"Extract embeddings for specific words.\"\"\"\n", "    embeddings = []\n", "    valid_words = []\n", "    \n", "    for word in words:\n", "        if embedding_type == 'pytorch':\n", "            if word in pytorch_word2idx:\n", "                idx = pytorch_word2idx[word]\n", "                embeddings.append(pytorch_embeddings[idx])\n", "                valid_words.append(word)\n", "        elif embedding_type == 'gensim':\n", "            if word in gensim_word2idx:\n", "                idx = gensim_word2idx[word]\n", "                embeddings.append(gensim_embeddings[idx])\n", "                valid_words.append(word)\n", "        elif embedding_type == 'google' and google_available:\n", "            if word in google_model.key_to_index:\n", "                embeddings.append(google_model[word])\n", "                valid_words.append(word)\n", "    \n", "    return np.array(embeddings), valid_words\n", "\n", "# Get embeddings for each type\n", "pytorch_word_embs, pytorch_words = get_word_embeddings_for_visualization(sample_words, 'pytorch')\n", "gensim_word_embs, gensim_words = get_word_embeddings_for_visualization(sample_words, 'gensim')\n", "\n", "if google_available:\n", "    google_word_embs, google_words = get_word_embeddings_for_visualization(sample_words, 'google')\n", "\n", "print(f\"PyTorch: {len(pytorch_words)} words found\")\n", "print(f\"Gensim: {len(gensim_words)} words found\")\n", "if google_available:\n", "    print(f\"Google: {len(google_words)} words found\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/png": "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**********************************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", "text/plain": ["<Figure size 1800x600 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create PCA visualization\n", "fig, axes = plt.subplots(1, 3 if google_available else 2, figsize=(18, 6))\n", "if not google_available:\n", "    axes = [axes[0], axes[1]]\n", "\n", "colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']\n", "word_categories = {\n", "    'animals': ['cat', 'dog', 'animal', 'pet'],\n", "    'objects': ['mat', 'rug', 'floor', 'chair'],\n", "    'actions': ['sit', 'sitting', 'lay', 'rest'],\n", "    'weather': ['weather', 'nice', 'today', 'sun'],\n", "    'academic': ['mathematics', 'difficult', 'subject', 'study'],\n", "    'descriptive': ['comfortable', 'soft', 'furry', 'good']\n", "}\n", "\n", "def plot_pca_embeddings(embeddings, words, title, ax):\n", "    \"\"\"Plot PCA visualization of embeddings.\"\"\"\n", "    if len(embeddings) < 2:\n", "        ax.text(0.5, 0.5, 'Insufficient data', ha='center', va='center', transform=ax.transAxes)\n", "        ax.set_title(title)\n", "        return\n", "    \n", "    # Apply PCA\n", "    pca = PCA(n_components=2)\n", "    embeddings_2d = pca.fit_transform(embeddings)\n", "    \n", "    # Plot points with category colors\n", "    for i, word in enumerate(words):\n", "        # Find category\n", "        color = 'black'  # default\n", "        for j, (category, cat_words) in enumerate(word_categories.items()):\n", "            if word in cat_words:\n", "                color = colors[j]\n", "                break\n", "        \n", "        ax.scatter(embeddings_2d[i, 0], embeddings_2d[i, 1], c=color, s=100, alpha=0.7)\n", "        ax.annotate(word, (embeddings_2d[i, 0], embeddings_2d[i, 1]), \n", "                   xytext=(5, 5), textcoords='offset points', fontsize=9)\n", "    \n", "    ax.set_title(f'{title}\\nPCA Visualization', fontweight='bold')\n", "    ax.set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.1%} variance)')\n", "    ax.set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.1%} variance)')\n", "    ax.grid(True, alpha=0.3)\n", "\n", "# Plot PCA for each embedding type\n", "plot_pca_embeddings(pytorch_word_embs, pytorch_words, 'PyTorch Word2Vec', axes[0])\n", "plot_pca_embeddings(gensim_word_embs, gensim_words, 'Gensim Word2Vec', axes[1])\n", "\n", "if google_available:\n", "    plot_pca_embeddings(google_word_embs, google_words, 'Google News Word2Vec', axes[2])\n", "\n", "# Add legend\n", "legend_elements = [plt.Line2D([0], [0], marker='o', color='w', markerfacecolor=colors[i], \n", "                             markersize=8, label=cat.title()) \n", "                  for i, cat in enumerate(word_categories.keys())]\n", "fig.legend(handles=legend_elements, loc='upper center', bbox_to_anchor=(0.5, 0.02), ncol=6)\n", "\n", "plt.suptitle('Word Embedding Clustering - PCA Analysis', fontsize=16, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/png": "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*********************************************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", "text/plain": ["<Figure size 1800x600 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create t-SNE visualization for better non-linear clustering\n", "fig, axes = plt.subplots(1, 3 if google_available else 2, figsize=(18, 6))\n", "if not google_available:\n", "    axes = [axes[0], axes[1]]\n", "\n", "def plot_tsne_embeddings(embeddings, words, title, ax):\n", "    \"\"\"Plot t-SNE visualization of embeddings.\"\"\"\n", "    if len(embeddings) < 2:\n", "        ax.text(0.5, 0.5, 'Insufficient data', ha='center', va='center', transform=ax.transAxes)\n", "        ax.set_title(title)\n", "        return\n", "    \n", "    # Apply t-SNE (use perplexity based on data size)\n", "    perplexity = min(30, len(embeddings) - 1)\n", "    if perplexity < 5:\n", "        perplexity = max(1, len(embeddings) - 1)\n", "    \n", "    tsne = TSNE(n_components=2, perplexity=perplexity, random_state=42, max_iter=1000)\n", "    embeddings_2d = tsne.fit_transform(embeddings)\n", "    \n", "    # Plot points with category colors\n", "    for i, word in enumerate(words):\n", "        # Find category\n", "        color = 'black'  # default\n", "        for j, (category, cat_words) in enumerate(word_categories.items()):\n", "            if word in cat_words:\n", "                color = colors[j]\n", "                break\n", "        \n", "        ax.scatter(embeddings_2d[i, 0], embeddings_2d[i, 1], c=color, s=100, alpha=0.7)\n", "        ax.annotate(word, (embeddings_2d[i, 0], embeddings_2d[i, 1]), \n", "                   xytext=(5, 5), textcoords='offset points', fontsize=9)\n", "    \n", "    ax.set_title(f'{title}\\nt-SNE Visualization', fontweight='bold')\n", "    ax.set_xlabel('t-SNE Dimension 1')\n", "    ax.set_ylabel('t-SNE Dimension 2')\n", "    ax.grid(True, alpha=0.3)\n", "\n", "# Plot t-SNE for each embedding type\n", "plot_tsne_embeddings(pytorch_word_embs, pytorch_words, 'PyTorch Word2Vec', axes[0])\n", "plot_tsne_embeddings(gensim_word_embs, gensim_words, 'Gensim Word2Vec', axes[1])\n", "\n", "if google_available:\n", "    plot_tsne_embeddings(google_word_embs, google_words, 'Google News Word2Vec', axes[2])\n", "\n", "# Add legend\n", "legend_elements = [plt.Line2D([0], [0], marker='o', color='w', markerfacecolor=colors[i], \n", "                             markersize=8, label=cat.title()) \n", "                  for i, cat in enumerate(word_categories.keys())]\n", "fig.legend(handles=legend_elements, loc='upper center', bbox_to_anchor=(0.5, 0.02), ncol=6)\n", "\n", "plt.suptitle('Word Embedding Clustering - t-SNE Analysis', fontsize=16, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "stat359-su25-ri0wcPwF-py3.12", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 4}