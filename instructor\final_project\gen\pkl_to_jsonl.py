import pickle
import json
import argparse
from tqdm import tqdm

# Usage: python pkl_to_jsonl.py --input input.pkl --output output.jsonl

def pkl_to_jsonl(input_pkl, output_jsonl):
    with open(input_pkl, 'rb') as pf:
        data = pickle.load(pf)
    # If data is a dict, get values; if list, use as is
    if isinstance(data, dict):
        items = data.values()
    else:
        items = data
    with open(output_jsonl, 'w', encoding='utf-8') as jf:
        for item in tqdm(items, desc=f"Writing to {output_jsonl}"):
            # Only keep 'name' and 'text' in each utterance
            cleaned = []
            for utt in item:
                cleaned.append({
                    'name': utt.get('name', ''),
                    'text': utt.get('text', '')
                })
            json.dump({"conversation": cleaned}, jf, ensure_ascii=False)
            jf.write('\n')

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Convert a pickle file of conversations to JSONL format.")
    parser.add_argument('--input', required=True, help='Input .pkl file')
    parser.add_argument('--output', required=True, help='Output .jsonl file')
    args = parser.parse_args()
    pkl_to_jsonl(args.input, args.output)
