<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head>

<meta charset="utf-8">
<meta name="generator" content="quarto-1.7.32">

<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">


<title>Assignment 2: Inspecting and Comparing Word Embeddings</title>
<style>
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
ul.task-list{list-style: none;}
ul.task-list li input[type="checkbox"] {
  width: 0.8em;
  margin: 0 0.8em 0.2em -1em; /* quarto-specific, see https://github.com/quarto-dev/quarto-cli/issues/4556 */ 
  vertical-align: middle;
}
/* CSS for syntax highlighting */
html { -webkit-text-size-adjust: 100%; }
pre > code.sourceCode { white-space: pre; position: relative; }
pre > code.sourceCode > span { display: inline-block; line-height: 1.25; }
pre > code.sourceCode > span:empty { height: 1.2em; }
.sourceCode { overflow: visible; }
code.sourceCode > span { color: inherit; text-decoration: inherit; }
div.sourceCode { margin: 1em 0; }
pre.sourceCode { margin: 0; }
@media screen {
div.sourceCode { overflow: auto; }
}
@media print {
pre > code.sourceCode { white-space: pre-wrap; }
pre > code.sourceCode > span { text-indent: -5em; padding-left: 5em; }
}
pre.numberSource code
  { counter-reset: source-line 0; }
pre.numberSource code > span
  { position: relative; left: -4em; counter-increment: source-line; }
pre.numberSource code > span > a:first-child::before
  { content: counter(source-line);
    position: relative; left: -1em; text-align: right; vertical-align: baseline;
    border: none; display: inline-block;
    -webkit-touch-callout: none; -webkit-user-select: none;
    -khtml-user-select: none; -moz-user-select: none;
    -ms-user-select: none; user-select: none;
    padding: 0 4px; width: 4em;
  }
pre.numberSource { margin-left: 3em;  padding-left: 4px; }
div.sourceCode
  {   }
@media screen {
pre > code.sourceCode > span > a:first-child::before { text-decoration: underline; }
}
</style>


<script src="inspect_embeddings_files/libs/clipboard/clipboard.min.js"></script>
<script src="inspect_embeddings_files/libs/quarto-html/quarto.js" type="module"></script>
<script src="inspect_embeddings_files/libs/quarto-html/tabsets/tabsets.js" type="module"></script>
<script src="inspect_embeddings_files/libs/quarto-html/popper.min.js"></script>
<script src="inspect_embeddings_files/libs/quarto-html/tippy.umd.min.js"></script>
<script src="inspect_embeddings_files/libs/quarto-html/anchor.min.js"></script>
<link href="inspect_embeddings_files/libs/quarto-html/tippy.css" rel="stylesheet">
<link href="inspect_embeddings_files/libs/quarto-html/quarto-syntax-highlighting-37eea08aefeeee20ff55810ff984fec1.css" rel="stylesheet" id="quarto-text-highlighting-styles">
<script src="inspect_embeddings_files/libs/bootstrap/bootstrap.min.js"></script>
<link href="inspect_embeddings_files/libs/bootstrap/bootstrap-icons.css" rel="stylesheet">
<link href="inspect_embeddings_files/libs/bootstrap/bootstrap-bb462d781dde1847d9e3ccf7736099dd.min.css" rel="stylesheet" append-hash="true" id="quarto-bootstrap" data-mode="light">


</head>

<body class="fullcontent quarto-light">

<div id="quarto-content" class="page-columns page-rows-contents page-layout-article">

<main class="content" id="quarto-document-content">

<header id="title-block-header" class="quarto-title-block default">
<div class="quarto-title">
<h1 class="title">Assignment 2: Inspecting and Comparing Word Embeddings</h1>
</div>



<div class="quarto-title-meta">

    
  
    
  </div>
  


</header>


<p>This notebook compares four different types of word embeddings: 1. <strong>Our PyTorch Word2Vec</strong> - Custom implementation with negative sampling 2. <strong>Gensim Word2Vec</strong> - Trained on text8 corpus 3. <strong>Pretrained Google News</strong> - Word2Vec trained on Google News corpus 4. <strong>Random Baseline</strong> - Random vectors for comparison</p>
<p>We’ll evaluate them using sentence similarity tasks.</p>
<div id="cell-2" class="cell" data-execution_count="1">
<div class="sourceCode cell-code" id="cb1"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="im">import</span> pickle</span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="im">import</span> numpy <span class="im">as</span> np</span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a><span class="im">import</span> pandas <span class="im">as</span> pd</span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a><span class="im">from</span> sklearn.metrics.pairwise <span class="im">import</span> cosine_similarity</span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a><span class="im">from</span> gensim.models <span class="im">import</span> Word2Vec, KeyedVectors</span>
<span id="cb1-6"><a href="#cb1-6" aria-hidden="true" tabindex="-1"></a><span class="im">import</span> warnings</span>
<span id="cb1-7"><a href="#cb1-7" aria-hidden="true" tabindex="-1"></a>warnings.filterwarnings(<span class="st">'ignore'</span>)</span>
<span id="cb1-8"><a href="#cb1-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-9"><a href="#cb1-9" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"Libraries loaded successfully!"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>Libraries loaded successfully!</code></pre>
</div>
</div>
<section id="load-all-embedding-types" class="level2">
<h2 class="anchored" data-anchor-id="load-all-embedding-types">1. Load All Embedding Types</h2>
<div id="cell-4" class="cell" data-execution_count="2">
<div class="sourceCode cell-code" id="cb3"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Load PyTorch Word2Vec embeddings</span></span>
<span id="cb3-2"><a href="#cb3-2" aria-hidden="true" tabindex="-1"></a><span class="cf">with</span> <span class="bu">open</span>(<span class="st">'test_word2vec_embeddings.pkl'</span>, <span class="st">'rb'</span>) <span class="im">as</span> f:</span>
<span id="cb3-3"><a href="#cb3-3" aria-hidden="true" tabindex="-1"></a>    pytorch_data <span class="op">=</span> pickle.load(f)</span>
<span id="cb3-4"><a href="#cb3-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-5"><a href="#cb3-5" aria-hidden="true" tabindex="-1"></a>pytorch_embeddings <span class="op">=</span> pytorch_data[<span class="st">'embeddings'</span>]</span>
<span id="cb3-6"><a href="#cb3-6" aria-hidden="true" tabindex="-1"></a>pytorch_word2idx <span class="op">=</span> pytorch_data[<span class="st">'word2idx'</span>]</span>
<span id="cb3-7"><a href="#cb3-7" aria-hidden="true" tabindex="-1"></a>pytorch_idx2word <span class="op">=</span> pytorch_data[<span class="st">'idx2word'</span>]</span>
<span id="cb3-8"><a href="#cb3-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-9"><a href="#cb3-9" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="ss">f"PyTorch Word2Vec: </span><span class="sc">{</span>pytorch_embeddings<span class="sc">.</span>shape[<span class="dv">0</span>]<span class="sc">}</span><span class="ss"> words, </span><span class="sc">{</span>pytorch_embeddings<span class="sc">.</span>shape[<span class="dv">1</span>]<span class="sc">}</span><span class="ss"> dimensions"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>PyTorch Word2Vec: 253854 words, 50 dimensions</code></pre>
</div>
</div>
<div id="cell-5" class="cell" data-execution_count="3">
<div class="sourceCode cell-code" id="cb5"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Load Gensim Word2Vec embeddings</span></span>
<span id="cb5-2"><a href="#cb5-2" aria-hidden="true" tabindex="-1"></a><span class="cf">with</span> <span class="bu">open</span>(<span class="st">'word2vec_gensim_embeddings.pkl'</span>, <span class="st">'rb'</span>) <span class="im">as</span> f:</span>
<span id="cb5-3"><a href="#cb5-3" aria-hidden="true" tabindex="-1"></a>    gensim_data <span class="op">=</span> pickle.load(f)</span>
<span id="cb5-4"><a href="#cb5-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-5"><a href="#cb5-5" aria-hidden="true" tabindex="-1"></a>gensim_embeddings <span class="op">=</span> gensim_data[<span class="st">'embeddings'</span>]</span>
<span id="cb5-6"><a href="#cb5-6" aria-hidden="true" tabindex="-1"></a>gensim_word2idx <span class="op">=</span> gensim_data[<span class="st">'word2idx'</span>]</span>
<span id="cb5-7"><a href="#cb5-7" aria-hidden="true" tabindex="-1"></a>gensim_idx2word <span class="op">=</span> gensim_data[<span class="st">'idx2word'</span>]</span>
<span id="cb5-8"><a href="#cb5-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-9"><a href="#cb5-9" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="ss">f"Gensim Word2Vec: </span><span class="sc">{</span>gensim_embeddings<span class="sc">.</span>shape[<span class="dv">0</span>]<span class="sc">}</span><span class="ss"> words, </span><span class="sc">{</span>gensim_embeddings<span class="sc">.</span>shape[<span class="dv">1</span>]<span class="sc">}</span><span class="ss"> dimensions"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>Gensim Word2Vec: 18497 words, 100 dimensions</code></pre>
</div>
</div>
<div id="cell-6" class="cell" data-execution_count="4">
<div class="sourceCode cell-code" id="cb7"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb7-1"><a href="#cb7-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Load Pretrained Google News Word2Vec</span></span>
<span id="cb7-2"><a href="#cb7-2" aria-hidden="true" tabindex="-1"></a><span class="cf">try</span>:</span>
<span id="cb7-3"><a href="#cb7-3" aria-hidden="true" tabindex="-1"></a>    google_model <span class="op">=</span> KeyedVectors.load(<span class="st">'word2vec-google-news-300.model'</span>)</span>
<span id="cb7-4"><a href="#cb7-4" aria-hidden="true" tabindex="-1"></a>    <span class="bu">print</span>(<span class="ss">f"Google News Word2Vec: </span><span class="sc">{</span><span class="bu">len</span>(google_model.key_to_index)<span class="sc">}</span><span class="ss"> words, </span><span class="sc">{</span>google_model<span class="sc">.</span>vector_size<span class="sc">}</span><span class="ss"> dimensions"</span>)</span>
<span id="cb7-5"><a href="#cb7-5" aria-hidden="true" tabindex="-1"></a>    google_available <span class="op">=</span> <span class="va">True</span></span>
<span id="cb7-6"><a href="#cb7-6" aria-hidden="true" tabindex="-1"></a><span class="cf">except</span> <span class="pp">Exception</span> <span class="im">as</span> e:</span>
<span id="cb7-7"><a href="#cb7-7" aria-hidden="true" tabindex="-1"></a>    <span class="bu">print</span>(<span class="ss">f"Could not load Google News model: </span><span class="sc">{</span>e<span class="sc">}</span><span class="ss">"</span>)</span>
<span id="cb7-8"><a href="#cb7-8" aria-hidden="true" tabindex="-1"></a>    google_available <span class="op">=</span> <span class="va">False</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>Google News Word2Vec: 3000000 words, 300 dimensions</code></pre>
</div>
</div>
</section>
<section id="helper-functions-for-sentence-embeddings" class="level2">
<h2 class="anchored" data-anchor-id="helper-functions-for-sentence-embeddings">2. Helper Functions for Sentence Embeddings</h2>
<div id="cell-8" class="cell" data-execution_count="5">
<div class="sourceCode cell-code" id="cb9"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb9-1"><a href="#cb9-1" aria-hidden="true" tabindex="-1"></a><span class="kw">def</span> get_sentence_embedding_pytorch(sentence, embeddings, word2idx, embedding_dim<span class="op">=</span><span class="dv">50</span>):</span>
<span id="cb9-2"><a href="#cb9-2" aria-hidden="true" tabindex="-1"></a>    <span class="co">"""Compute sentence embedding by averaging word embeddings (PyTorch)."""</span></span>
<span id="cb9-3"><a href="#cb9-3" aria-hidden="true" tabindex="-1"></a>    words <span class="op">=</span> sentence.lower().split()</span>
<span id="cb9-4"><a href="#cb9-4" aria-hidden="true" tabindex="-1"></a>    word_vectors <span class="op">=</span> []</span>
<span id="cb9-5"><a href="#cb9-5" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-6"><a href="#cb9-6" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> word <span class="kw">in</span> words:</span>
<span id="cb9-7"><a href="#cb9-7" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> word <span class="kw">in</span> word2idx:</span>
<span id="cb9-8"><a href="#cb9-8" aria-hidden="true" tabindex="-1"></a>            idx <span class="op">=</span> word2idx[word]</span>
<span id="cb9-9"><a href="#cb9-9" aria-hidden="true" tabindex="-1"></a>            word_vectors.append(embeddings[idx])</span>
<span id="cb9-10"><a href="#cb9-10" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-11"><a href="#cb9-11" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> word_vectors:</span>
<span id="cb9-12"><a href="#cb9-12" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> np.mean(word_vectors, axis<span class="op">=</span><span class="dv">0</span>)</span>
<span id="cb9-13"><a href="#cb9-13" aria-hidden="true" tabindex="-1"></a>    <span class="cf">else</span>:</span>
<span id="cb9-14"><a href="#cb9-14" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> np.zeros(embedding_dim)</span>
<span id="cb9-15"><a href="#cb9-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-16"><a href="#cb9-16" aria-hidden="true" tabindex="-1"></a><span class="kw">def</span> get_sentence_embedding_gensim(sentence, embeddings, word2idx, embedding_dim<span class="op">=</span><span class="dv">100</span>):</span>
<span id="cb9-17"><a href="#cb9-17" aria-hidden="true" tabindex="-1"></a>    <span class="co">"""Compute sentence embedding by averaging word embeddings (Gensim)."""</span></span>
<span id="cb9-18"><a href="#cb9-18" aria-hidden="true" tabindex="-1"></a>    words <span class="op">=</span> sentence.lower().split()</span>
<span id="cb9-19"><a href="#cb9-19" aria-hidden="true" tabindex="-1"></a>    word_vectors <span class="op">=</span> []</span>
<span id="cb9-20"><a href="#cb9-20" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-21"><a href="#cb9-21" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> word <span class="kw">in</span> words:</span>
<span id="cb9-22"><a href="#cb9-22" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> word <span class="kw">in</span> word2idx:</span>
<span id="cb9-23"><a href="#cb9-23" aria-hidden="true" tabindex="-1"></a>            idx <span class="op">=</span> word2idx[word]</span>
<span id="cb9-24"><a href="#cb9-24" aria-hidden="true" tabindex="-1"></a>            word_vectors.append(embeddings[idx])</span>
<span id="cb9-25"><a href="#cb9-25" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-26"><a href="#cb9-26" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> word_vectors:</span>
<span id="cb9-27"><a href="#cb9-27" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> np.mean(word_vectors, axis<span class="op">=</span><span class="dv">0</span>)</span>
<span id="cb9-28"><a href="#cb9-28" aria-hidden="true" tabindex="-1"></a>    <span class="cf">else</span>:</span>
<span id="cb9-29"><a href="#cb9-29" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> np.zeros(embedding_dim)</span>
<span id="cb9-30"><a href="#cb9-30" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-31"><a href="#cb9-31" aria-hidden="true" tabindex="-1"></a><span class="kw">def</span> get_sentence_embedding_google(sentence, model):</span>
<span id="cb9-32"><a href="#cb9-32" aria-hidden="true" tabindex="-1"></a>    <span class="co">"""Compute sentence embedding using Google News Word2Vec."""</span></span>
<span id="cb9-33"><a href="#cb9-33" aria-hidden="true" tabindex="-1"></a>    words <span class="op">=</span> sentence.lower().split()</span>
<span id="cb9-34"><a href="#cb9-34" aria-hidden="true" tabindex="-1"></a>    word_vectors <span class="op">=</span> []</span>
<span id="cb9-35"><a href="#cb9-35" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-36"><a href="#cb9-36" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> word <span class="kw">in</span> words:</span>
<span id="cb9-37"><a href="#cb9-37" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> word <span class="kw">in</span> model.key_to_index:</span>
<span id="cb9-38"><a href="#cb9-38" aria-hidden="true" tabindex="-1"></a>            word_vectors.append(model[word])</span>
<span id="cb9-39"><a href="#cb9-39" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-40"><a href="#cb9-40" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> word_vectors:</span>
<span id="cb9-41"><a href="#cb9-41" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> np.mean(word_vectors, axis<span class="op">=</span><span class="dv">0</span>)</span>
<span id="cb9-42"><a href="#cb9-42" aria-hidden="true" tabindex="-1"></a>    <span class="cf">else</span>:</span>
<span id="cb9-43"><a href="#cb9-43" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> np.zeros(model.vector_size)</span>
<span id="cb9-44"><a href="#cb9-44" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-45"><a href="#cb9-45" aria-hidden="true" tabindex="-1"></a><span class="kw">def</span> get_sentence_embedding_random(sentence, embedding_dim<span class="op">=</span><span class="dv">100</span>):</span>
<span id="cb9-46"><a href="#cb9-46" aria-hidden="true" tabindex="-1"></a>    <span class="co">"""Generate random sentence embedding for baseline comparison."""</span></span>
<span id="cb9-47"><a href="#cb9-47" aria-hidden="true" tabindex="-1"></a>    np.random.seed(<span class="bu">hash</span>(sentence) <span class="op">%</span> <span class="dv">2</span><span class="op">**</span><span class="dv">32</span>)  <span class="co"># Consistent random for same sentence</span></span>
<span id="cb9-48"><a href="#cb9-48" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> np.random.normal(<span class="dv">0</span>, <span class="dv">1</span>, embedding_dim)</span>
<span id="cb9-49"><a href="#cb9-49" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-50"><a href="#cb9-50" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"Helper functions defined!"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>Helper functions defined!</code></pre>
</div>
</div>
</section>
<section id="define-test-sentences-for-similarity-comparison" class="level2">
<h2 class="anchored" data-anchor-id="define-test-sentences-for-similarity-comparison">3. Define Test Sentences for Similarity Comparison</h2>
<div id="cell-10" class="cell" data-execution_count="6">
<div class="sourceCode cell-code" id="cb11"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb11-1"><a href="#cb11-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Query sentence</span></span>
<span id="cb11-2"><a href="#cb11-2" aria-hidden="true" tabindex="-1"></a>query_sentence <span class="op">=</span> <span class="st">"The cat sat on the mat"</span></span>
<span id="cb11-3"><a href="#cb11-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-4"><a href="#cb11-4" aria-hidden="true" tabindex="-1"></a><span class="co"># Comparison sentences with expected similarity rankings</span></span>
<span id="cb11-5"><a href="#cb11-5" aria-hidden="true" tabindex="-1"></a>comparison_sentences <span class="op">=</span> [</span>
<span id="cb11-6"><a href="#cb11-6" aria-hidden="true" tabindex="-1"></a>    <span class="st">"A cat is sitting on a rug"</span>,           <span class="co"># High similarity (same meaning)</span></span>
<span id="cb11-7"><a href="#cb11-7" aria-hidden="true" tabindex="-1"></a>    <span class="st">"The dog lay on the floor"</span>,            <span class="co"># Medium similarity (similar structure, different animals)</span></span>
<span id="cb11-8"><a href="#cb11-8" aria-hidden="true" tabindex="-1"></a>    <span class="st">"Animals like to rest comfortably"</span>,    <span class="co"># Medium similarity (related concept)</span></span>
<span id="cb11-9"><a href="#cb11-9" aria-hidden="true" tabindex="-1"></a>    <span class="st">"The weather is nice today"</span>,           <span class="co"># Low similarity (unrelated)</span></span>
<span id="cb11-10"><a href="#cb11-10" aria-hidden="true" tabindex="-1"></a>    <span class="st">"Mathematics is a difficult subject"</span>,   <span class="co"># Low similarity (completely unrelated)</span></span>
<span id="cb11-11"><a href="#cb11-11" aria-hidden="true" tabindex="-1"></a>    <span class="st">"Cats are furry pets"</span>,                 <span class="co"># Medium similarity (mentions cats)</span></span>
<span id="cb11-12"><a href="#cb11-12" aria-hidden="true" tabindex="-1"></a>    <span class="st">"The mat was very soft"</span>,               <span class="co"># Medium similarity (mentions mat)</span></span>
<span id="cb11-13"><a href="#cb11-13" aria-hidden="true" tabindex="-1"></a>    <span class="st">"Sitting is comfortable"</span>,              <span class="co"># Low-medium similarity (mentions sitting)</span></span>
<span id="cb11-14"><a href="#cb11-14" aria-hidden="true" tabindex="-1"></a>]</span>
<span id="cb11-15"><a href="#cb11-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-16"><a href="#cb11-16" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="ss">f"Query: '</span><span class="sc">{</span>query_sentence<span class="sc">}</span><span class="ss">'"</span>)</span>
<span id="cb11-17"><a href="#cb11-17" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="ss">f"</span><span class="ch">\n</span><span class="ss">Comparing against </span><span class="sc">{</span><span class="bu">len</span>(comparison_sentences)<span class="sc">}</span><span class="ss"> sentences:"</span>)</span>
<span id="cb11-18"><a href="#cb11-18" aria-hidden="true" tabindex="-1"></a><span class="cf">for</span> i, sent <span class="kw">in</span> <span class="bu">enumerate</span>(comparison_sentences, <span class="dv">1</span>):</span>
<span id="cb11-19"><a href="#cb11-19" aria-hidden="true" tabindex="-1"></a>    <span class="bu">print</span>(<span class="ss">f"</span><span class="sc">{</span>i<span class="sc">}</span><span class="ss">. </span><span class="sc">{</span>sent<span class="sc">}</span><span class="ss">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>Query: 'The cat sat on the mat'

Comparing against 8 sentences:
1. A cat is sitting on a rug
2. The dog lay on the floor
3. Animals like to rest comfortably
4. The weather is nice today
5. Mathematics is a difficult subject
6. Cats are furry pets
7. The mat was very soft
8. Sitting is comfortable</code></pre>
</div>
</div>
</section>
<section id="compute-sentence-similarities-for-all-embedding-types" class="level2">
<h2 class="anchored" data-anchor-id="compute-sentence-similarities-for-all-embedding-types">4. Compute Sentence Similarities for All Embedding Types</h2>
<div id="cell-12" class="cell" data-execution_count="7">
<div class="sourceCode cell-code" id="cb13"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb13-1"><a href="#cb13-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Compute query embedding for each model</span></span>
<span id="cb13-2"><a href="#cb13-2" aria-hidden="true" tabindex="-1"></a>query_pytorch <span class="op">=</span> get_sentence_embedding_pytorch(query_sentence, pytorch_embeddings, pytorch_word2idx, <span class="dv">50</span>)</span>
<span id="cb13-3"><a href="#cb13-3" aria-hidden="true" tabindex="-1"></a>query_gensim <span class="op">=</span> get_sentence_embedding_gensim(query_sentence, gensim_embeddings, gensim_word2idx, <span class="dv">100</span>)</span>
<span id="cb13-4"><a href="#cb13-4" aria-hidden="true" tabindex="-1"></a>query_random <span class="op">=</span> get_sentence_embedding_random(query_sentence, <span class="dv">100</span>)</span>
<span id="cb13-5"><a href="#cb13-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb13-6"><a href="#cb13-6" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span> google_available:</span>
<span id="cb13-7"><a href="#cb13-7" aria-hidden="true" tabindex="-1"></a>    query_google <span class="op">=</span> get_sentence_embedding_google(query_sentence, google_model)</span>
<span id="cb13-8"><a href="#cb13-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb13-9"><a href="#cb13-9" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"Query embeddings computed for all models!"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>Query embeddings computed for all models!</code></pre>
</div>
</div>
<div id="cell-13" class="cell" data-execution_count="8">
<div class="sourceCode cell-code" id="cb15"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb15-1"><a href="#cb15-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Compute similarities for all comparison sentences</span></span>
<span id="cb15-2"><a href="#cb15-2" aria-hidden="true" tabindex="-1"></a>results <span class="op">=</span> []</span>
<span id="cb15-3"><a href="#cb15-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-4"><a href="#cb15-4" aria-hidden="true" tabindex="-1"></a><span class="cf">for</span> i, sentence <span class="kw">in</span> <span class="bu">enumerate</span>(comparison_sentences):</span>
<span id="cb15-5"><a href="#cb15-5" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Get embeddings for this sentence</span></span>
<span id="cb15-6"><a href="#cb15-6" aria-hidden="true" tabindex="-1"></a>    sent_pytorch <span class="op">=</span> get_sentence_embedding_pytorch(sentence, pytorch_embeddings, pytorch_word2idx, <span class="dv">50</span>)</span>
<span id="cb15-7"><a href="#cb15-7" aria-hidden="true" tabindex="-1"></a>    sent_gensim <span class="op">=</span> get_sentence_embedding_gensim(sentence, gensim_embeddings, gensim_word2idx, <span class="dv">100</span>)</span>
<span id="cb15-8"><a href="#cb15-8" aria-hidden="true" tabindex="-1"></a>    sent_random <span class="op">=</span> get_sentence_embedding_random(sentence, <span class="dv">100</span>)</span>
<span id="cb15-9"><a href="#cb15-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-10"><a href="#cb15-10" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Compute cosine similarities</span></span>
<span id="cb15-11"><a href="#cb15-11" aria-hidden="true" tabindex="-1"></a>    sim_pytorch <span class="op">=</span> cosine_similarity([query_pytorch], [sent_pytorch])[<span class="dv">0</span>][<span class="dv">0</span>]</span>
<span id="cb15-12"><a href="#cb15-12" aria-hidden="true" tabindex="-1"></a>    sim_gensim <span class="op">=</span> cosine_similarity([query_gensim], [sent_gensim])[<span class="dv">0</span>][<span class="dv">0</span>]</span>
<span id="cb15-13"><a href="#cb15-13" aria-hidden="true" tabindex="-1"></a>    sim_random <span class="op">=</span> cosine_similarity([query_random], [sent_random])[<span class="dv">0</span>][<span class="dv">0</span>]</span>
<span id="cb15-14"><a href="#cb15-14" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-15"><a href="#cb15-15" aria-hidden="true" tabindex="-1"></a>    result <span class="op">=</span> {</span>
<span id="cb15-16"><a href="#cb15-16" aria-hidden="true" tabindex="-1"></a>        <span class="st">'sentence'</span>: sentence,</span>
<span id="cb15-17"><a href="#cb15-17" aria-hidden="true" tabindex="-1"></a>        <span class="st">'pytorch_sim'</span>: sim_pytorch,</span>
<span id="cb15-18"><a href="#cb15-18" aria-hidden="true" tabindex="-1"></a>        <span class="st">'gensim_sim'</span>: sim_gensim,</span>
<span id="cb15-19"><a href="#cb15-19" aria-hidden="true" tabindex="-1"></a>        <span class="st">'random_sim'</span>: sim_random</span>
<span id="cb15-20"><a href="#cb15-20" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb15-21"><a href="#cb15-21" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-22"><a href="#cb15-22" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> google_available:</span>
<span id="cb15-23"><a href="#cb15-23" aria-hidden="true" tabindex="-1"></a>        sent_google <span class="op">=</span> get_sentence_embedding_google(sentence, google_model)</span>
<span id="cb15-24"><a href="#cb15-24" aria-hidden="true" tabindex="-1"></a>        sim_google <span class="op">=</span> cosine_similarity([query_google], [sent_google])[<span class="dv">0</span>][<span class="dv">0</span>]</span>
<span id="cb15-25"><a href="#cb15-25" aria-hidden="true" tabindex="-1"></a>        result[<span class="st">'google_sim'</span>] <span class="op">=</span> sim_google</span>
<span id="cb15-26"><a href="#cb15-26" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb15-27"><a href="#cb15-27" aria-hidden="true" tabindex="-1"></a>    results.append(result)</span>
<span id="cb15-28"><a href="#cb15-28" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-29"><a href="#cb15-29" aria-hidden="true" tabindex="-1"></a><span class="co"># Create DataFrame for easy analysis</span></span>
<span id="cb15-30"><a href="#cb15-30" aria-hidden="true" tabindex="-1"></a>df_results <span class="op">=</span> pd.DataFrame(results)</span>
<span id="cb15-31"><a href="#cb15-31" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"Similarities computed for all sentences!"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>Similarities computed for all sentences!</code></pre>
</div>
</div>
</section>
<section id="display-and-analyze-results" class="level2">
<h2 class="anchored" data-anchor-id="display-and-analyze-results">5. Display and Analyze Results</h2>
<div id="cell-15" class="cell" data-execution_count="9">
<div class="sourceCode cell-code" id="cb17"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb17-1"><a href="#cb17-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Display results table</span></span>
<span id="cb17-2"><a href="#cb17-2" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="ss">f"</span><span class="ch">\n</span><span class="sc">{</span><span class="st">'='</span><span class="op">*</span><span class="dv">80</span><span class="sc">}</span><span class="ss">"</span>)</span>
<span id="cb17-3"><a href="#cb17-3" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="ss">f"SENTENCE SIMILARITY COMPARISON"</span>)</span>
<span id="cb17-4"><a href="#cb17-4" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="ss">f"Query: '</span><span class="sc">{</span>query_sentence<span class="sc">}</span><span class="ss">'"</span>)</span>
<span id="cb17-5"><a href="#cb17-5" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="ss">f"</span><span class="sc">{</span><span class="st">'='</span><span class="op">*</span><span class="dv">80</span><span class="sc">}</span><span class="ss">"</span>)</span>
<span id="cb17-6"><a href="#cb17-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-7"><a href="#cb17-7" aria-hidden="true" tabindex="-1"></a><span class="co"># Format and display results</span></span>
<span id="cb17-8"><a href="#cb17-8" aria-hidden="true" tabindex="-1"></a>pd.set_option(<span class="st">'display.max_columns'</span>, <span class="va">None</span>)</span>
<span id="cb17-9"><a href="#cb17-9" aria-hidden="true" tabindex="-1"></a>pd.set_option(<span class="st">'display.width'</span>, <span class="va">None</span>)</span>
<span id="cb17-10"><a href="#cb17-10" aria-hidden="true" tabindex="-1"></a>pd.set_option(<span class="st">'display.max_colwidth'</span>, <span class="dv">50</span>)</span>
<span id="cb17-11"><a href="#cb17-11" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-12"><a href="#cb17-12" aria-hidden="true" tabindex="-1"></a><span class="co"># Round similarities for better display</span></span>
<span id="cb17-13"><a href="#cb17-13" aria-hidden="true" tabindex="-1"></a>display_df <span class="op">=</span> df_results.copy()</span>
<span id="cb17-14"><a href="#cb17-14" aria-hidden="true" tabindex="-1"></a><span class="cf">for</span> col <span class="kw">in</span> [<span class="st">'pytorch_sim'</span>, <span class="st">'gensim_sim'</span>, <span class="st">'random_sim'</span>]:</span>
<span id="cb17-15"><a href="#cb17-15" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> col <span class="kw">in</span> display_df.columns:</span>
<span id="cb17-16"><a href="#cb17-16" aria-hidden="true" tabindex="-1"></a>        display_df[col] <span class="op">=</span> display_df[col].<span class="bu">round</span>(<span class="dv">4</span>)</span>
<span id="cb17-17"><a href="#cb17-17" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span> <span class="st">'google_sim'</span> <span class="kw">in</span> display_df.columns:</span>
<span id="cb17-18"><a href="#cb17-18" aria-hidden="true" tabindex="-1"></a>    display_df[<span class="st">'google_sim'</span>] <span class="op">=</span> display_df[<span class="st">'google_sim'</span>].<span class="bu">round</span>(<span class="dv">4</span>)</span>
<span id="cb17-19"><a href="#cb17-19" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-20"><a href="#cb17-20" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(display_df.to_string(index<span class="op">=</span><span class="va">False</span>))</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
================================================================================
SENTENCE SIMILARITY COMPARISON
Query: 'The cat sat on the mat'
================================================================================
                          sentence  pytorch_sim  gensim_sim  random_sim  google_sim
         A cat is sitting on a rug       0.9594      0.6055     -0.1016      0.7552
          The dog lay on the floor       0.9996      0.9040     -0.1519      0.7736
  Animals like to rest comfortably       0.9326      0.2027      0.0274      0.4742
         The weather is nice today       0.9911      0.2260      0.2179      0.3829
Mathematics is a difficult subject       0.9563      0.1210     -0.0979      0.2272
               Cats are furry pets       0.7830     -0.0988     -0.0036      0.4243
             The mat was very soft       0.9891      0.2418     -0.0461      0.6417
            Sitting is comfortable       0.8638      0.1662      0.0584      0.5023</code></pre>
</div>
</div>
<div id="cell-16" class="cell" data-execution_count="10">
<div class="sourceCode cell-code" id="cb19"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb19-1"><a href="#cb19-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Rank sentences by similarity for each embedding type</span></span>
<span id="cb19-2"><a href="#cb19-2" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="ss">f"</span><span class="ch">\n</span><span class="sc">{</span><span class="st">'='</span><span class="op">*</span><span class="dv">80</span><span class="sc">}</span><span class="ss">"</span>)</span>
<span id="cb19-3"><a href="#cb19-3" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"RANKING BY SIMILARITY (Highest to Lowest)"</span>)</span>
<span id="cb19-4"><a href="#cb19-4" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="ss">f"</span><span class="sc">{</span><span class="st">'='</span><span class="op">*</span><span class="dv">80</span><span class="sc">}</span><span class="ss">"</span>)</span>
<span id="cb19-5"><a href="#cb19-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-6"><a href="#cb19-6" aria-hidden="true" tabindex="-1"></a>embedding_types <span class="op">=</span> [<span class="st">'pytorch_sim'</span>, <span class="st">'gensim_sim'</span>, <span class="st">'random_sim'</span>]</span>
<span id="cb19-7"><a href="#cb19-7" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span> google_available:</span>
<span id="cb19-8"><a href="#cb19-8" aria-hidden="true" tabindex="-1"></a>    embedding_types.append(<span class="st">'google_sim'</span>)</span>
<span id="cb19-9"><a href="#cb19-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-10"><a href="#cb19-10" aria-hidden="true" tabindex="-1"></a><span class="cf">for</span> emb_type <span class="kw">in</span> embedding_types:</span>
<span id="cb19-11"><a href="#cb19-11" aria-hidden="true" tabindex="-1"></a>    <span class="bu">print</span>(<span class="ss">f"</span><span class="ch">\n</span><span class="sc">{</span>emb_type<span class="sc">.</span>replace(<span class="st">'_sim'</span>, <span class="st">''</span>)<span class="sc">.</span>upper()<span class="sc">}</span><span class="ss"> WORD2VEC RANKING:"</span>)</span>
<span id="cb19-12"><a href="#cb19-12" aria-hidden="true" tabindex="-1"></a>    ranked <span class="op">=</span> df_results.sort_values(emb_type, ascending<span class="op">=</span><span class="va">False</span>)</span>
<span id="cb19-13"><a href="#cb19-13" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> i, (_, row) <span class="kw">in</span> <span class="bu">enumerate</span>(ranked.iterrows(), <span class="dv">1</span>):</span>
<span id="cb19-14"><a href="#cb19-14" aria-hidden="true" tabindex="-1"></a>        <span class="bu">print</span>(<span class="ss">f"</span><span class="sc">{</span>i<span class="sc">}</span><span class="ss">. </span><span class="sc">{</span>row[<span class="st">'sentence'</span>][:<span class="dv">60</span>]<span class="sc">:&lt;60}</span><span class="ss"> (sim: </span><span class="sc">{</span>row[emb_type]<span class="sc">:.4f}</span><span class="ss">)"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
================================================================================
RANKING BY SIMILARITY (Highest to Lowest)
================================================================================

PYTORCH WORD2VEC RANKING:
1. The dog lay on the floor                                     (sim: 0.9996)
2. The weather is nice today                                    (sim: 0.9911)
3. The mat was very soft                                        (sim: 0.9891)
4. A cat is sitting on a rug                                    (sim: 0.9594)
5. Mathematics is a difficult subject                           (sim: 0.9563)
6. Animals like to rest comfortably                             (sim: 0.9326)
7. Sitting is comfortable                                       (sim: 0.8638)
8. Cats are furry pets                                          (sim: 0.7830)

GENSIM WORD2VEC RANKING:
1. The dog lay on the floor                                     (sim: 0.9040)
2. A cat is sitting on a rug                                    (sim: 0.6055)
3. The mat was very soft                                        (sim: 0.2418)
4. The weather is nice today                                    (sim: 0.2260)
5. Animals like to rest comfortably                             (sim: 0.2027)
6. Sitting is comfortable                                       (sim: 0.1662)
7. Mathematics is a difficult subject                           (sim: 0.1210)
8. Cats are furry pets                                          (sim: -0.0988)

RANDOM WORD2VEC RANKING:
1. The weather is nice today                                    (sim: 0.2179)
2. Sitting is comfortable                                       (sim: 0.0584)
3. Animals like to rest comfortably                             (sim: 0.0274)
4. Cats are furry pets                                          (sim: -0.0036)
5. The mat was very soft                                        (sim: -0.0461)
6. Mathematics is a difficult subject                           (sim: -0.0979)
7. A cat is sitting on a rug                                    (sim: -0.1016)
8. The dog lay on the floor                                     (sim: -0.1519)

GOOGLE WORD2VEC RANKING:
1. The dog lay on the floor                                     (sim: 0.7736)
2. A cat is sitting on a rug                                    (sim: 0.7552)
3. The mat was very soft                                        (sim: 0.6417)
4. Sitting is comfortable                                       (sim: 0.5023)
5. Animals like to rest comfortably                             (sim: 0.4742)
6. Cats are furry pets                                          (sim: 0.4243)
7. The weather is nice today                                    (sim: 0.3829)
8. Mathematics is a difficult subject                           (sim: 0.2272)</code></pre>
</div>
</div>
</section>
<section id="analysis-and-comparison" class="level2">
<h2 class="anchored" data-anchor-id="analysis-and-comparison">6. Analysis and Comparison</h2>
<div id="cell-18" class="cell" data-execution_count="11">
<div class="sourceCode cell-code" id="cb21"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb21-1"><a href="#cb21-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Analyze which sentences should be most similar (ground truth)</span></span>
<span id="cb21-2"><a href="#cb21-2" aria-hidden="true" tabindex="-1"></a>expected_high_similarity <span class="op">=</span> [</span>
<span id="cb21-3"><a href="#cb21-3" aria-hidden="true" tabindex="-1"></a>    <span class="st">"A cat is sitting on a rug"</span>,  <span class="co"># Should be #1 - same meaning</span></span>
<span id="cb21-4"><a href="#cb21-4" aria-hidden="true" tabindex="-1"></a>    <span class="st">"The dog lay on the floor"</span>,   <span class="co"># Should be #2 - similar structure</span></span>
<span id="cb21-5"><a href="#cb21-5" aria-hidden="true" tabindex="-1"></a>]</span>
<span id="cb21-6"><a href="#cb21-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-7"><a href="#cb21-7" aria-hidden="true" tabindex="-1"></a>expected_medium_similarity <span class="op">=</span> [</span>
<span id="cb21-8"><a href="#cb21-8" aria-hidden="true" tabindex="-1"></a>    <span class="st">"Animals like to rest comfortably"</span>,</span>
<span id="cb21-9"><a href="#cb21-9" aria-hidden="true" tabindex="-1"></a>    <span class="st">"Cats are furry pets"</span>,</span>
<span id="cb21-10"><a href="#cb21-10" aria-hidden="true" tabindex="-1"></a>    <span class="st">"The mat was very soft"</span>,</span>
<span id="cb21-11"><a href="#cb21-11" aria-hidden="true" tabindex="-1"></a>]</span>
<span id="cb21-12"><a href="#cb21-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-13"><a href="#cb21-13" aria-hidden="true" tabindex="-1"></a>expected_low_similarity <span class="op">=</span> [</span>
<span id="cb21-14"><a href="#cb21-14" aria-hidden="true" tabindex="-1"></a>    <span class="st">"The weather is nice today"</span>,</span>
<span id="cb21-15"><a href="#cb21-15" aria-hidden="true" tabindex="-1"></a>    <span class="st">"Mathematics is a difficult subject"</span>,</span>
<span id="cb21-16"><a href="#cb21-16" aria-hidden="true" tabindex="-1"></a>]</span>
<span id="cb21-17"><a href="#cb21-17" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-18"><a href="#cb21-18" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"</span><span class="ch">\n</span><span class="st">"</span> <span class="op">+</span> <span class="st">"="</span><span class="op">*</span><span class="dv">80</span>)</span>
<span id="cb21-19"><a href="#cb21-19" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"ANALYSIS: How Well Do Embeddings Capture Semantic Similarity?"</span>)</span>
<span id="cb21-20"><a href="#cb21-20" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"="</span><span class="op">*</span><span class="dv">80</span>)</span>
<span id="cb21-21"><a href="#cb21-21" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-22"><a href="#cb21-22" aria-hidden="true" tabindex="-1"></a><span class="kw">def</span> analyze_ranking(emb_type, df):</span>
<span id="cb21-23"><a href="#cb21-23" aria-hidden="true" tabindex="-1"></a>    ranked <span class="op">=</span> df.sort_values(emb_type, ascending<span class="op">=</span><span class="va">False</span>)</span>
<span id="cb21-24"><a href="#cb21-24" aria-hidden="true" tabindex="-1"></a>    top_sentences <span class="op">=</span> ranked[<span class="st">'sentence'</span>].head(<span class="dv">3</span>).tolist()</span>
<span id="cb21-25"><a href="#cb21-25" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb21-26"><a href="#cb21-26" aria-hidden="true" tabindex="-1"></a>    <span class="bu">print</span>(<span class="ss">f"</span><span class="ch">\n</span><span class="sc">{</span>emb_type<span class="sc">.</span>replace(<span class="st">'_sim'</span>, <span class="st">''</span>)<span class="sc">.</span>upper()<span class="sc">}</span><span class="ss"> Analysis:"</span>)</span>
<span id="cb21-27"><a href="#cb21-27" aria-hidden="true" tabindex="-1"></a>    <span class="bu">print</span>(<span class="ss">f"Top 3 most similar: </span><span class="sc">{</span>top_sentences<span class="sc">}</span><span class="ss">"</span>)</span>
<span id="cb21-28"><a href="#cb21-28" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb21-29"><a href="#cb21-29" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Check if expected high similarity sentences are in top 3</span></span>
<span id="cb21-30"><a href="#cb21-30" aria-hidden="true" tabindex="-1"></a>    high_sim_in_top3 <span class="op">=</span> <span class="bu">sum</span>(<span class="dv">1</span> <span class="cf">for</span> sent <span class="kw">in</span> expected_high_similarity <span class="cf">if</span> sent <span class="kw">in</span> top_sentences)</span>
<span id="cb21-31"><a href="#cb21-31" aria-hidden="true" tabindex="-1"></a>    <span class="bu">print</span>(<span class="ss">f"Expected high-similarity sentences in top 3: </span><span class="sc">{</span>high_sim_in_top3<span class="sc">}</span><span class="ss">/2"</span>)</span>
<span id="cb21-32"><a href="#cb21-32" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb21-33"><a href="#cb21-33" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Check ranking of the most similar sentence</span></span>
<span id="cb21-34"><a href="#cb21-34" aria-hidden="true" tabindex="-1"></a>    most_similar_expected <span class="op">=</span> <span class="st">"A cat is sitting on a rug"</span></span>
<span id="cb21-35"><a href="#cb21-35" aria-hidden="true" tabindex="-1"></a>    rank <span class="op">=</span> ranked[ranked[<span class="st">'sentence'</span>] <span class="op">==</span> most_similar_expected].index[<span class="dv">0</span>] <span class="op">+</span> <span class="dv">1</span></span>
<span id="cb21-36"><a href="#cb21-36" aria-hidden="true" tabindex="-1"></a>    <span class="bu">print</span>(<span class="ss">f"Rank of most semantically similar sentence: #</span><span class="sc">{</span>rank<span class="sc">}</span><span class="ss">"</span>)</span>
<span id="cb21-37"><a href="#cb21-37" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb21-38"><a href="#cb21-38" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> high_sim_in_top3, rank</span>
<span id="cb21-39"><a href="#cb21-39" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-40"><a href="#cb21-40" aria-hidden="true" tabindex="-1"></a><span class="co"># Analyze each embedding type</span></span>
<span id="cb21-41"><a href="#cb21-41" aria-hidden="true" tabindex="-1"></a>analysis_results <span class="op">=</span> {}</span>
<span id="cb21-42"><a href="#cb21-42" aria-hidden="true" tabindex="-1"></a><span class="cf">for</span> emb_type <span class="kw">in</span> embedding_types:</span>
<span id="cb21-43"><a href="#cb21-43" aria-hidden="true" tabindex="-1"></a>    high_score, rank <span class="op">=</span> analyze_ranking(emb_type, df_results)</span>
<span id="cb21-44"><a href="#cb21-44" aria-hidden="true" tabindex="-1"></a>    analysis_results[emb_type] <span class="op">=</span> {<span class="st">'high_score'</span>: high_score, <span class="st">'best_rank'</span>: rank}</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
================================================================================
ANALYSIS: How Well Do Embeddings Capture Semantic Similarity?
================================================================================

PYTORCH Analysis:
Top 3 most similar: ['The dog lay on the floor', 'The weather is nice today', 'The mat was very soft']
Expected high-similarity sentences in top 3: 1/2
Rank of most semantically similar sentence: #1

GENSIM Analysis:
Top 3 most similar: ['The dog lay on the floor', 'A cat is sitting on a rug', 'The mat was very soft']
Expected high-similarity sentences in top 3: 2/2
Rank of most semantically similar sentence: #1

RANDOM Analysis:
Top 3 most similar: ['The weather is nice today', 'Sitting is comfortable', 'Animals like to rest comfortably']
Expected high-similarity sentences in top 3: 0/2
Rank of most semantically similar sentence: #1

GOOGLE Analysis:
Top 3 most similar: ['The dog lay on the floor', 'A cat is sitting on a rug', 'The mat was very soft']
Expected high-similarity sentences in top 3: 2/2
Rank of most semantically similar sentence: #1</code></pre>
</div>
</div>
<div id="cell-19" class="cell" data-execution_count="12">
<div class="sourceCode cell-code" id="cb23"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb23-1"><a href="#cb23-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Summary comparison</span></span>
<span id="cb23-2"><a href="#cb23-2" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"</span><span class="ch">\n</span><span class="st">"</span> <span class="op">+</span> <span class="st">"="</span><span class="op">*</span><span class="dv">80</span>)</span>
<span id="cb23-3"><a href="#cb23-3" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"FINAL COMPARISON SUMMARY"</span>)</span>
<span id="cb23-4"><a href="#cb23-4" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"="</span><span class="op">*</span><span class="dv">80</span>)</span>
<span id="cb23-5"><a href="#cb23-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb23-6"><a href="#cb23-6" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"</span><span class="ch">\n</span><span class="st">Performance Ranking (Best to Worst):"</span>)</span>
<span id="cb23-7"><a href="#cb23-7" aria-hidden="true" tabindex="-1"></a>performance_scores <span class="op">=</span> []</span>
<span id="cb23-8"><a href="#cb23-8" aria-hidden="true" tabindex="-1"></a><span class="cf">for</span> emb_type, results <span class="kw">in</span> analysis_results.items():</span>
<span id="cb23-9"><a href="#cb23-9" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Simple scoring: high_score (0-2) + bonus for rank 1 (3 points), rank 2 (2 points), rank 3 (1 point)</span></span>
<span id="cb23-10"><a href="#cb23-10" aria-hidden="true" tabindex="-1"></a>    rank_bonus <span class="op">=</span> <span class="bu">max</span>(<span class="dv">0</span>, <span class="dv">4</span> <span class="op">-</span> results[<span class="st">'best_rank'</span>])</span>
<span id="cb23-11"><a href="#cb23-11" aria-hidden="true" tabindex="-1"></a>    total_score <span class="op">=</span> results[<span class="st">'high_score'</span>] <span class="op">+</span> rank_bonus</span>
<span id="cb23-12"><a href="#cb23-12" aria-hidden="true" tabindex="-1"></a>    performance_scores.append((emb_type, total_score, results))</span>
<span id="cb23-13"><a href="#cb23-13" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb23-14"><a href="#cb23-14" aria-hidden="true" tabindex="-1"></a>performance_scores.sort(key<span class="op">=</span><span class="kw">lambda</span> x: x[<span class="dv">1</span>], reverse<span class="op">=</span><span class="va">True</span>)</span>
<span id="cb23-15"><a href="#cb23-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb23-16"><a href="#cb23-16" aria-hidden="true" tabindex="-1"></a><span class="cf">for</span> i, (emb_type, score, results) <span class="kw">in</span> <span class="bu">enumerate</span>(performance_scores, <span class="dv">1</span>):</span>
<span id="cb23-17"><a href="#cb23-17" aria-hidden="true" tabindex="-1"></a>    name <span class="op">=</span> emb_type.replace(<span class="st">'_sim'</span>, <span class="st">''</span>).upper()</span>
<span id="cb23-18"><a href="#cb23-18" aria-hidden="true" tabindex="-1"></a>    <span class="bu">print</span>(<span class="ss">f"</span><span class="sc">{</span>i<span class="sc">}</span><span class="ss">. </span><span class="sc">{</span>name<span class="sc">:&lt;15}</span><span class="ss"> (Score: </span><span class="sc">{</span>score<span class="sc">}</span><span class="ss">/5, Top sentences: </span><span class="sc">{</span>results[<span class="st">'high_score'</span>]<span class="sc">}</span><span class="ss">/2, Best rank: #</span><span class="sc">{</span>results[<span class="st">'best_rank'</span>]<span class="sc">}</span><span class="ss">)"</span>)</span>
<span id="cb23-19"><a href="#cb23-19" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb23-20"><a href="#cb23-20" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"</span><span class="ch">\n</span><span class="st">Key Insights:"</span>)</span>
<span id="cb23-21"><a href="#cb23-21" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"• Higher scores indicate better semantic understanding"</span>)</span>
<span id="cb23-22"><a href="#cb23-22" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"• Random baseline should perform worst (as expected)"</span>)</span>
<span id="cb23-23"><a href="#cb23-23" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"• Pretrained models typically outperform custom models due to larger training data"</span>)</span>
<span id="cb23-24"><a href="#cb23-24" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"• Gensim and PyTorch models show how implementation affects results"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
================================================================================
FINAL COMPARISON SUMMARY
================================================================================

Performance Ranking (Best to Worst):
1. GENSIM          (Score: 5/5, Top sentences: 2/2, Best rank: #1)
2. GOOGLE          (Score: 5/5, Top sentences: 2/2, Best rank: #1)
3. PYTORCH         (Score: 4/5, Top sentences: 1/2, Best rank: #1)
4. RANDOM          (Score: 3/5, Top sentences: 0/2, Best rank: #1)

Key Insights:
• Higher scores indicate better semantic understanding
• Random baseline should perform worst (as expected)
• Pretrained models typically outperform custom models due to larger training data
• Gensim and PyTorch models show how implementation affects results</code></pre>
</div>
</div>
</section>
<section id="clustering-visualizations-pca-and-t-sne" class="level2">
<h2 class="anchored" data-anchor-id="clustering-visualizations-pca-and-t-sne">7. Clustering Visualizations (PCA and t-SNE)</h2>
<p>Let’s visualize how different embedding types cluster words in 2D space using dimensionality reduction techniques.</p>
<div id="cell-21" class="cell" data-execution_count="13">
<div class="sourceCode cell-code" id="cb25"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb25-1"><a href="#cb25-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Import additional libraries for clustering visualization</span></span>
<span id="cb25-2"><a href="#cb25-2" aria-hidden="true" tabindex="-1"></a><span class="im">from</span> sklearn.decomposition <span class="im">import</span> PCA</span>
<span id="cb25-3"><a href="#cb25-3" aria-hidden="true" tabindex="-1"></a><span class="im">from</span> sklearn.manifold <span class="im">import</span> TSNE</span>
<span id="cb25-4"><a href="#cb25-4" aria-hidden="true" tabindex="-1"></a><span class="im">import</span> matplotlib.pyplot <span class="im">as</span> plt</span>
<span id="cb25-5"><a href="#cb25-5" aria-hidden="true" tabindex="-1"></a><span class="im">import</span> numpy <span class="im">as</span> np</span>
<span id="cb25-6"><a href="#cb25-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb25-7"><a href="#cb25-7" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"Clustering libraries imported!"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>Clustering libraries imported!</code></pre>
</div>
</div>
<div id="cell-22" class="cell" data-execution_count="14">
<div class="sourceCode cell-code" id="cb27"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb27-1"><a href="#cb27-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Select sample words for visualization</span></span>
<span id="cb27-2"><a href="#cb27-2" aria-hidden="true" tabindex="-1"></a>sample_words <span class="op">=</span> [</span>
<span id="cb27-3"><a href="#cb27-3" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Animals</span></span>
<span id="cb27-4"><a href="#cb27-4" aria-hidden="true" tabindex="-1"></a>    <span class="st">'cat'</span>, <span class="st">'dog'</span>, <span class="st">'animal'</span>, <span class="st">'pet'</span>,</span>
<span id="cb27-5"><a href="#cb27-5" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Furniture/Objects</span></span>
<span id="cb27-6"><a href="#cb27-6" aria-hidden="true" tabindex="-1"></a>    <span class="st">'mat'</span>, <span class="st">'rug'</span>, <span class="st">'floor'</span>, <span class="st">'chair'</span>,</span>
<span id="cb27-7"><a href="#cb27-7" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Actions</span></span>
<span id="cb27-8"><a href="#cb27-8" aria-hidden="true" tabindex="-1"></a>    <span class="st">'sit'</span>, <span class="st">'sitting'</span>, <span class="st">'lay'</span>, <span class="st">'rest'</span>,</span>
<span id="cb27-9"><a href="#cb27-9" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Weather/Nature</span></span>
<span id="cb27-10"><a href="#cb27-10" aria-hidden="true" tabindex="-1"></a>    <span class="st">'weather'</span>, <span class="st">'nice'</span>, <span class="st">'today'</span>, <span class="st">'sun'</span>,</span>
<span id="cb27-11"><a href="#cb27-11" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Academic</span></span>
<span id="cb27-12"><a href="#cb27-12" aria-hidden="true" tabindex="-1"></a>    <span class="st">'mathematics'</span>, <span class="st">'difficult'</span>, <span class="st">'subject'</span>, <span class="st">'study'</span>,</span>
<span id="cb27-13"><a href="#cb27-13" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Descriptive</span></span>
<span id="cb27-14"><a href="#cb27-14" aria-hidden="true" tabindex="-1"></a>    <span class="st">'comfortable'</span>, <span class="st">'soft'</span>, <span class="st">'furry'</span>, <span class="st">'good'</span></span>
<span id="cb27-15"><a href="#cb27-15" aria-hidden="true" tabindex="-1"></a>]</span>
<span id="cb27-16"><a href="#cb27-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb27-17"><a href="#cb27-17" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="ss">f"Selected </span><span class="sc">{</span><span class="bu">len</span>(sample_words)<span class="sc">}</span><span class="ss"> words for clustering visualization"</span>)</span>
<span id="cb27-18"><a href="#cb27-18" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="ss">f"Words: </span><span class="sc">{</span>sample_words<span class="sc">}</span><span class="ss">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>Selected 24 words for clustering visualization
Words: ['cat', 'dog', 'animal', 'pet', 'mat', 'rug', 'floor', 'chair', 'sit', 'sitting', 'lay', 'rest', 'weather', 'nice', 'today', 'sun', 'mathematics', 'difficult', 'subject', 'study', 'comfortable', 'soft', 'furry', 'good']</code></pre>
</div>
</div>
<div id="cell-23" class="cell" data-execution_count="15">
<div class="sourceCode cell-code" id="cb29"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb29-1"><a href="#cb29-1" aria-hidden="true" tabindex="-1"></a><span class="kw">def</span> get_word_embeddings_for_visualization(words, embedding_type):</span>
<span id="cb29-2"><a href="#cb29-2" aria-hidden="true" tabindex="-1"></a>    <span class="co">"""Extract embeddings for specific words."""</span></span>
<span id="cb29-3"><a href="#cb29-3" aria-hidden="true" tabindex="-1"></a>    embeddings <span class="op">=</span> []</span>
<span id="cb29-4"><a href="#cb29-4" aria-hidden="true" tabindex="-1"></a>    valid_words <span class="op">=</span> []</span>
<span id="cb29-5"><a href="#cb29-5" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb29-6"><a href="#cb29-6" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> word <span class="kw">in</span> words:</span>
<span id="cb29-7"><a href="#cb29-7" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> embedding_type <span class="op">==</span> <span class="st">'pytorch'</span>:</span>
<span id="cb29-8"><a href="#cb29-8" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> word <span class="kw">in</span> pytorch_word2idx:</span>
<span id="cb29-9"><a href="#cb29-9" aria-hidden="true" tabindex="-1"></a>                idx <span class="op">=</span> pytorch_word2idx[word]</span>
<span id="cb29-10"><a href="#cb29-10" aria-hidden="true" tabindex="-1"></a>                embeddings.append(pytorch_embeddings[idx])</span>
<span id="cb29-11"><a href="#cb29-11" aria-hidden="true" tabindex="-1"></a>                valid_words.append(word)</span>
<span id="cb29-12"><a href="#cb29-12" aria-hidden="true" tabindex="-1"></a>        <span class="cf">elif</span> embedding_type <span class="op">==</span> <span class="st">'gensim'</span>:</span>
<span id="cb29-13"><a href="#cb29-13" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> word <span class="kw">in</span> gensim_word2idx:</span>
<span id="cb29-14"><a href="#cb29-14" aria-hidden="true" tabindex="-1"></a>                idx <span class="op">=</span> gensim_word2idx[word]</span>
<span id="cb29-15"><a href="#cb29-15" aria-hidden="true" tabindex="-1"></a>                embeddings.append(gensim_embeddings[idx])</span>
<span id="cb29-16"><a href="#cb29-16" aria-hidden="true" tabindex="-1"></a>                valid_words.append(word)</span>
<span id="cb29-17"><a href="#cb29-17" aria-hidden="true" tabindex="-1"></a>        <span class="cf">elif</span> embedding_type <span class="op">==</span> <span class="st">'google'</span> <span class="kw">and</span> google_available:</span>
<span id="cb29-18"><a href="#cb29-18" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> word <span class="kw">in</span> google_model.key_to_index:</span>
<span id="cb29-19"><a href="#cb29-19" aria-hidden="true" tabindex="-1"></a>                embeddings.append(google_model[word])</span>
<span id="cb29-20"><a href="#cb29-20" aria-hidden="true" tabindex="-1"></a>                valid_words.append(word)</span>
<span id="cb29-21"><a href="#cb29-21" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb29-22"><a href="#cb29-22" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> np.array(embeddings), valid_words</span>
<span id="cb29-23"><a href="#cb29-23" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb29-24"><a href="#cb29-24" aria-hidden="true" tabindex="-1"></a><span class="co"># Get embeddings for each type</span></span>
<span id="cb29-25"><a href="#cb29-25" aria-hidden="true" tabindex="-1"></a>pytorch_word_embs, pytorch_words <span class="op">=</span> get_word_embeddings_for_visualization(sample_words, <span class="st">'pytorch'</span>)</span>
<span id="cb29-26"><a href="#cb29-26" aria-hidden="true" tabindex="-1"></a>gensim_word_embs, gensim_words <span class="op">=</span> get_word_embeddings_for_visualization(sample_words, <span class="st">'gensim'</span>)</span>
<span id="cb29-27"><a href="#cb29-27" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb29-28"><a href="#cb29-28" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span> google_available:</span>
<span id="cb29-29"><a href="#cb29-29" aria-hidden="true" tabindex="-1"></a>    google_word_embs, google_words <span class="op">=</span> get_word_embeddings_for_visualization(sample_words, <span class="st">'google'</span>)</span>
<span id="cb29-30"><a href="#cb29-30" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb29-31"><a href="#cb29-31" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="ss">f"PyTorch: </span><span class="sc">{</span><span class="bu">len</span>(pytorch_words)<span class="sc">}</span><span class="ss"> words found"</span>)</span>
<span id="cb29-32"><a href="#cb29-32" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="ss">f"Gensim: </span><span class="sc">{</span><span class="bu">len</span>(gensim_words)<span class="sc">}</span><span class="ss"> words found"</span>)</span>
<span id="cb29-33"><a href="#cb29-33" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span> google_available:</span>
<span id="cb29-34"><a href="#cb29-34" aria-hidden="true" tabindex="-1"></a>    <span class="bu">print</span>(<span class="ss">f"Google: </span><span class="sc">{</span><span class="bu">len</span>(google_words)<span class="sc">}</span><span class="ss"> words found"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>PyTorch: 24 words found
Gensim: 22 words found
Google: 24 words found</code></pre>
</div>
</div>
<div id="cell-24" class="cell" data-execution_count="16">
<div class="sourceCode cell-code" id="cb31"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb31-1"><a href="#cb31-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Create PCA visualization</span></span>
<span id="cb31-2"><a href="#cb31-2" aria-hidden="true" tabindex="-1"></a>fig, axes <span class="op">=</span> plt.subplots(<span class="dv">1</span>, <span class="dv">3</span> <span class="cf">if</span> google_available <span class="cf">else</span> <span class="dv">2</span>, figsize<span class="op">=</span>(<span class="dv">18</span>, <span class="dv">6</span>))</span>
<span id="cb31-3"><a href="#cb31-3" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span> <span class="kw">not</span> google_available:</span>
<span id="cb31-4"><a href="#cb31-4" aria-hidden="true" tabindex="-1"></a>    axes <span class="op">=</span> [axes[<span class="dv">0</span>], axes[<span class="dv">1</span>]]</span>
<span id="cb31-5"><a href="#cb31-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb31-6"><a href="#cb31-6" aria-hidden="true" tabindex="-1"></a>colors <span class="op">=</span> [<span class="st">'red'</span>, <span class="st">'blue'</span>, <span class="st">'green'</span>, <span class="st">'orange'</span>, <span class="st">'purple'</span>, <span class="st">'brown'</span>]</span>
<span id="cb31-7"><a href="#cb31-7" aria-hidden="true" tabindex="-1"></a>word_categories <span class="op">=</span> {</span>
<span id="cb31-8"><a href="#cb31-8" aria-hidden="true" tabindex="-1"></a>    <span class="st">'animals'</span>: [<span class="st">'cat'</span>, <span class="st">'dog'</span>, <span class="st">'animal'</span>, <span class="st">'pet'</span>],</span>
<span id="cb31-9"><a href="#cb31-9" aria-hidden="true" tabindex="-1"></a>    <span class="st">'objects'</span>: [<span class="st">'mat'</span>, <span class="st">'rug'</span>, <span class="st">'floor'</span>, <span class="st">'chair'</span>],</span>
<span id="cb31-10"><a href="#cb31-10" aria-hidden="true" tabindex="-1"></a>    <span class="st">'actions'</span>: [<span class="st">'sit'</span>, <span class="st">'sitting'</span>, <span class="st">'lay'</span>, <span class="st">'rest'</span>],</span>
<span id="cb31-11"><a href="#cb31-11" aria-hidden="true" tabindex="-1"></a>    <span class="st">'weather'</span>: [<span class="st">'weather'</span>, <span class="st">'nice'</span>, <span class="st">'today'</span>, <span class="st">'sun'</span>],</span>
<span id="cb31-12"><a href="#cb31-12" aria-hidden="true" tabindex="-1"></a>    <span class="st">'academic'</span>: [<span class="st">'mathematics'</span>, <span class="st">'difficult'</span>, <span class="st">'subject'</span>, <span class="st">'study'</span>],</span>
<span id="cb31-13"><a href="#cb31-13" aria-hidden="true" tabindex="-1"></a>    <span class="st">'descriptive'</span>: [<span class="st">'comfortable'</span>, <span class="st">'soft'</span>, <span class="st">'furry'</span>, <span class="st">'good'</span>]</span>
<span id="cb31-14"><a href="#cb31-14" aria-hidden="true" tabindex="-1"></a>}</span>
<span id="cb31-15"><a href="#cb31-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb31-16"><a href="#cb31-16" aria-hidden="true" tabindex="-1"></a><span class="kw">def</span> plot_pca_embeddings(embeddings, words, title, ax):</span>
<span id="cb31-17"><a href="#cb31-17" aria-hidden="true" tabindex="-1"></a>    <span class="co">"""Plot PCA visualization of embeddings."""</span></span>
<span id="cb31-18"><a href="#cb31-18" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="bu">len</span>(embeddings) <span class="op">&lt;</span> <span class="dv">2</span>:</span>
<span id="cb31-19"><a href="#cb31-19" aria-hidden="true" tabindex="-1"></a>        ax.text(<span class="fl">0.5</span>, <span class="fl">0.5</span>, <span class="st">'Insufficient data'</span>, ha<span class="op">=</span><span class="st">'center'</span>, va<span class="op">=</span><span class="st">'center'</span>, transform<span class="op">=</span>ax.transAxes)</span>
<span id="cb31-20"><a href="#cb31-20" aria-hidden="true" tabindex="-1"></a>        ax.set_title(title)</span>
<span id="cb31-21"><a href="#cb31-21" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span></span>
<span id="cb31-22"><a href="#cb31-22" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb31-23"><a href="#cb31-23" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Apply PCA</span></span>
<span id="cb31-24"><a href="#cb31-24" aria-hidden="true" tabindex="-1"></a>    pca <span class="op">=</span> PCA(n_components<span class="op">=</span><span class="dv">2</span>)</span>
<span id="cb31-25"><a href="#cb31-25" aria-hidden="true" tabindex="-1"></a>    embeddings_2d <span class="op">=</span> pca.fit_transform(embeddings)</span>
<span id="cb31-26"><a href="#cb31-26" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb31-27"><a href="#cb31-27" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Plot points with category colors</span></span>
<span id="cb31-28"><a href="#cb31-28" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> i, word <span class="kw">in</span> <span class="bu">enumerate</span>(words):</span>
<span id="cb31-29"><a href="#cb31-29" aria-hidden="true" tabindex="-1"></a>        <span class="co"># Find category</span></span>
<span id="cb31-30"><a href="#cb31-30" aria-hidden="true" tabindex="-1"></a>        color <span class="op">=</span> <span class="st">'black'</span>  <span class="co"># default</span></span>
<span id="cb31-31"><a href="#cb31-31" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> j, (category, cat_words) <span class="kw">in</span> <span class="bu">enumerate</span>(word_categories.items()):</span>
<span id="cb31-32"><a href="#cb31-32" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> word <span class="kw">in</span> cat_words:</span>
<span id="cb31-33"><a href="#cb31-33" aria-hidden="true" tabindex="-1"></a>                color <span class="op">=</span> colors[j]</span>
<span id="cb31-34"><a href="#cb31-34" aria-hidden="true" tabindex="-1"></a>                <span class="cf">break</span></span>
<span id="cb31-35"><a href="#cb31-35" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb31-36"><a href="#cb31-36" aria-hidden="true" tabindex="-1"></a>        ax.scatter(embeddings_2d[i, <span class="dv">0</span>], embeddings_2d[i, <span class="dv">1</span>], c<span class="op">=</span>color, s<span class="op">=</span><span class="dv">100</span>, alpha<span class="op">=</span><span class="fl">0.7</span>)</span>
<span id="cb31-37"><a href="#cb31-37" aria-hidden="true" tabindex="-1"></a>        ax.annotate(word, (embeddings_2d[i, <span class="dv">0</span>], embeddings_2d[i, <span class="dv">1</span>]), </span>
<span id="cb31-38"><a href="#cb31-38" aria-hidden="true" tabindex="-1"></a>                   xytext<span class="op">=</span>(<span class="dv">5</span>, <span class="dv">5</span>), textcoords<span class="op">=</span><span class="st">'offset points'</span>, fontsize<span class="op">=</span><span class="dv">9</span>)</span>
<span id="cb31-39"><a href="#cb31-39" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb31-40"><a href="#cb31-40" aria-hidden="true" tabindex="-1"></a>    ax.set_title(<span class="ss">f'</span><span class="sc">{</span>title<span class="sc">}</span><span class="ch">\n</span><span class="ss">PCA Visualization'</span>, fontweight<span class="op">=</span><span class="st">'bold'</span>)</span>
<span id="cb31-41"><a href="#cb31-41" aria-hidden="true" tabindex="-1"></a>    ax.set_xlabel(<span class="ss">f'PC1 (</span><span class="sc">{</span>pca<span class="sc">.</span>explained_variance_ratio_[<span class="dv">0</span>]<span class="sc">:.1%}</span><span class="ss"> variance)'</span>)</span>
<span id="cb31-42"><a href="#cb31-42" aria-hidden="true" tabindex="-1"></a>    ax.set_ylabel(<span class="ss">f'PC2 (</span><span class="sc">{</span>pca<span class="sc">.</span>explained_variance_ratio_[<span class="dv">1</span>]<span class="sc">:.1%}</span><span class="ss"> variance)'</span>)</span>
<span id="cb31-43"><a href="#cb31-43" aria-hidden="true" tabindex="-1"></a>    ax.grid(<span class="va">True</span>, alpha<span class="op">=</span><span class="fl">0.3</span>)</span>
<span id="cb31-44"><a href="#cb31-44" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb31-45"><a href="#cb31-45" aria-hidden="true" tabindex="-1"></a><span class="co"># Plot PCA for each embedding type</span></span>
<span id="cb31-46"><a href="#cb31-46" aria-hidden="true" tabindex="-1"></a>plot_pca_embeddings(pytorch_word_embs, pytorch_words, <span class="st">'PyTorch Word2Vec'</span>, axes[<span class="dv">0</span>])</span>
<span id="cb31-47"><a href="#cb31-47" aria-hidden="true" tabindex="-1"></a>plot_pca_embeddings(gensim_word_embs, gensim_words, <span class="st">'Gensim Word2Vec'</span>, axes[<span class="dv">1</span>])</span>
<span id="cb31-48"><a href="#cb31-48" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb31-49"><a href="#cb31-49" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span> google_available:</span>
<span id="cb31-50"><a href="#cb31-50" aria-hidden="true" tabindex="-1"></a>    plot_pca_embeddings(google_word_embs, google_words, <span class="st">'Google News Word2Vec'</span>, axes[<span class="dv">2</span>])</span>
<span id="cb31-51"><a href="#cb31-51" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb31-52"><a href="#cb31-52" aria-hidden="true" tabindex="-1"></a><span class="co"># Add legend</span></span>
<span id="cb31-53"><a href="#cb31-53" aria-hidden="true" tabindex="-1"></a>legend_elements <span class="op">=</span> [plt.Line2D([<span class="dv">0</span>], [<span class="dv">0</span>], marker<span class="op">=</span><span class="st">'o'</span>, color<span class="op">=</span><span class="st">'w'</span>, markerfacecolor<span class="op">=</span>colors[i], </span>
<span id="cb31-54"><a href="#cb31-54" aria-hidden="true" tabindex="-1"></a>                             markersize<span class="op">=</span><span class="dv">8</span>, label<span class="op">=</span>cat.title()) </span>
<span id="cb31-55"><a href="#cb31-55" aria-hidden="true" tabindex="-1"></a>                  <span class="cf">for</span> i, cat <span class="kw">in</span> <span class="bu">enumerate</span>(word_categories.keys())]</span>
<span id="cb31-56"><a href="#cb31-56" aria-hidden="true" tabindex="-1"></a>fig.legend(handles<span class="op">=</span>legend_elements, loc<span class="op">=</span><span class="st">'upper center'</span>, bbox_to_anchor<span class="op">=</span>(<span class="fl">0.5</span>, <span class="fl">0.02</span>), ncol<span class="op">=</span><span class="dv">6</span>)</span>
<span id="cb31-57"><a href="#cb31-57" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb31-58"><a href="#cb31-58" aria-hidden="true" tabindex="-1"></a>plt.suptitle(<span class="st">'Word Embedding Clustering - PCA Analysis'</span>, fontsize<span class="op">=</span><span class="dv">16</span>, fontweight<span class="op">=</span><span class="st">'bold'</span>)</span>
<span id="cb31-59"><a href="#cb31-59" aria-hidden="true" tabindex="-1"></a>plt.tight_layout()</span>
<span id="cb31-60"><a href="#cb31-60" aria-hidden="true" tabindex="-1"></a>plt.show()</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-display">
<div>
<figure class="figure">
<p><img src="inspect_embeddings_files/figure-html/cell-17-output-1.png" class="img-fluid figure-img"></p>
</figure>
</div>
</div>
</div>
<div id="cell-25" class="cell" data-execution_count="17">
<div class="sourceCode cell-code" id="cb32"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb32-1"><a href="#cb32-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Create t-SNE visualization for better non-linear clustering</span></span>
<span id="cb32-2"><a href="#cb32-2" aria-hidden="true" tabindex="-1"></a>fig, axes <span class="op">=</span> plt.subplots(<span class="dv">1</span>, <span class="dv">3</span> <span class="cf">if</span> google_available <span class="cf">else</span> <span class="dv">2</span>, figsize<span class="op">=</span>(<span class="dv">18</span>, <span class="dv">6</span>))</span>
<span id="cb32-3"><a href="#cb32-3" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span> <span class="kw">not</span> google_available:</span>
<span id="cb32-4"><a href="#cb32-4" aria-hidden="true" tabindex="-1"></a>    axes <span class="op">=</span> [axes[<span class="dv">0</span>], axes[<span class="dv">1</span>]]</span>
<span id="cb32-5"><a href="#cb32-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb32-6"><a href="#cb32-6" aria-hidden="true" tabindex="-1"></a><span class="kw">def</span> plot_tsne_embeddings(embeddings, words, title, ax):</span>
<span id="cb32-7"><a href="#cb32-7" aria-hidden="true" tabindex="-1"></a>    <span class="co">"""Plot t-SNE visualization of embeddings."""</span></span>
<span id="cb32-8"><a href="#cb32-8" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="bu">len</span>(embeddings) <span class="op">&lt;</span> <span class="dv">2</span>:</span>
<span id="cb32-9"><a href="#cb32-9" aria-hidden="true" tabindex="-1"></a>        ax.text(<span class="fl">0.5</span>, <span class="fl">0.5</span>, <span class="st">'Insufficient data'</span>, ha<span class="op">=</span><span class="st">'center'</span>, va<span class="op">=</span><span class="st">'center'</span>, transform<span class="op">=</span>ax.transAxes)</span>
<span id="cb32-10"><a href="#cb32-10" aria-hidden="true" tabindex="-1"></a>        ax.set_title(title)</span>
<span id="cb32-11"><a href="#cb32-11" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span></span>
<span id="cb32-12"><a href="#cb32-12" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb32-13"><a href="#cb32-13" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Apply t-SNE (use perplexity based on data size)</span></span>
<span id="cb32-14"><a href="#cb32-14" aria-hidden="true" tabindex="-1"></a>    perplexity <span class="op">=</span> <span class="bu">min</span>(<span class="dv">30</span>, <span class="bu">len</span>(embeddings) <span class="op">-</span> <span class="dv">1</span>)</span>
<span id="cb32-15"><a href="#cb32-15" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> perplexity <span class="op">&lt;</span> <span class="dv">5</span>:</span>
<span id="cb32-16"><a href="#cb32-16" aria-hidden="true" tabindex="-1"></a>        perplexity <span class="op">=</span> <span class="bu">max</span>(<span class="dv">1</span>, <span class="bu">len</span>(embeddings) <span class="op">-</span> <span class="dv">1</span>)</span>
<span id="cb32-17"><a href="#cb32-17" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb32-18"><a href="#cb32-18" aria-hidden="true" tabindex="-1"></a>    tsne <span class="op">=</span> TSNE(n_components<span class="op">=</span><span class="dv">2</span>, perplexity<span class="op">=</span>perplexity, random_state<span class="op">=</span><span class="dv">42</span>, max_iter<span class="op">=</span><span class="dv">1000</span>)</span>
<span id="cb32-19"><a href="#cb32-19" aria-hidden="true" tabindex="-1"></a>    embeddings_2d <span class="op">=</span> tsne.fit_transform(embeddings)</span>
<span id="cb32-20"><a href="#cb32-20" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb32-21"><a href="#cb32-21" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Plot points with category colors</span></span>
<span id="cb32-22"><a href="#cb32-22" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> i, word <span class="kw">in</span> <span class="bu">enumerate</span>(words):</span>
<span id="cb32-23"><a href="#cb32-23" aria-hidden="true" tabindex="-1"></a>        <span class="co"># Find category</span></span>
<span id="cb32-24"><a href="#cb32-24" aria-hidden="true" tabindex="-1"></a>        color <span class="op">=</span> <span class="st">'black'</span>  <span class="co"># default</span></span>
<span id="cb32-25"><a href="#cb32-25" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> j, (category, cat_words) <span class="kw">in</span> <span class="bu">enumerate</span>(word_categories.items()):</span>
<span id="cb32-26"><a href="#cb32-26" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> word <span class="kw">in</span> cat_words:</span>
<span id="cb32-27"><a href="#cb32-27" aria-hidden="true" tabindex="-1"></a>                color <span class="op">=</span> colors[j]</span>
<span id="cb32-28"><a href="#cb32-28" aria-hidden="true" tabindex="-1"></a>                <span class="cf">break</span></span>
<span id="cb32-29"><a href="#cb32-29" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb32-30"><a href="#cb32-30" aria-hidden="true" tabindex="-1"></a>        ax.scatter(embeddings_2d[i, <span class="dv">0</span>], embeddings_2d[i, <span class="dv">1</span>], c<span class="op">=</span>color, s<span class="op">=</span><span class="dv">100</span>, alpha<span class="op">=</span><span class="fl">0.7</span>)</span>
<span id="cb32-31"><a href="#cb32-31" aria-hidden="true" tabindex="-1"></a>        ax.annotate(word, (embeddings_2d[i, <span class="dv">0</span>], embeddings_2d[i, <span class="dv">1</span>]), </span>
<span id="cb32-32"><a href="#cb32-32" aria-hidden="true" tabindex="-1"></a>                   xytext<span class="op">=</span>(<span class="dv">5</span>, <span class="dv">5</span>), textcoords<span class="op">=</span><span class="st">'offset points'</span>, fontsize<span class="op">=</span><span class="dv">9</span>)</span>
<span id="cb32-33"><a href="#cb32-33" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb32-34"><a href="#cb32-34" aria-hidden="true" tabindex="-1"></a>    ax.set_title(<span class="ss">f'</span><span class="sc">{</span>title<span class="sc">}</span><span class="ch">\n</span><span class="ss">t-SNE Visualization'</span>, fontweight<span class="op">=</span><span class="st">'bold'</span>)</span>
<span id="cb32-35"><a href="#cb32-35" aria-hidden="true" tabindex="-1"></a>    ax.set_xlabel(<span class="st">'t-SNE Dimension 1'</span>)</span>
<span id="cb32-36"><a href="#cb32-36" aria-hidden="true" tabindex="-1"></a>    ax.set_ylabel(<span class="st">'t-SNE Dimension 2'</span>)</span>
<span id="cb32-37"><a href="#cb32-37" aria-hidden="true" tabindex="-1"></a>    ax.grid(<span class="va">True</span>, alpha<span class="op">=</span><span class="fl">0.3</span>)</span>
<span id="cb32-38"><a href="#cb32-38" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb32-39"><a href="#cb32-39" aria-hidden="true" tabindex="-1"></a><span class="co"># Plot t-SNE for each embedding type</span></span>
<span id="cb32-40"><a href="#cb32-40" aria-hidden="true" tabindex="-1"></a>plot_tsne_embeddings(pytorch_word_embs, pytorch_words, <span class="st">'PyTorch Word2Vec'</span>, axes[<span class="dv">0</span>])</span>
<span id="cb32-41"><a href="#cb32-41" aria-hidden="true" tabindex="-1"></a>plot_tsne_embeddings(gensim_word_embs, gensim_words, <span class="st">'Gensim Word2Vec'</span>, axes[<span class="dv">1</span>])</span>
<span id="cb32-42"><a href="#cb32-42" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb32-43"><a href="#cb32-43" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span> google_available:</span>
<span id="cb32-44"><a href="#cb32-44" aria-hidden="true" tabindex="-1"></a>    plot_tsne_embeddings(google_word_embs, google_words, <span class="st">'Google News Word2Vec'</span>, axes[<span class="dv">2</span>])</span>
<span id="cb32-45"><a href="#cb32-45" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb32-46"><a href="#cb32-46" aria-hidden="true" tabindex="-1"></a><span class="co"># Add legend</span></span>
<span id="cb32-47"><a href="#cb32-47" aria-hidden="true" tabindex="-1"></a>legend_elements <span class="op">=</span> [plt.Line2D([<span class="dv">0</span>], [<span class="dv">0</span>], marker<span class="op">=</span><span class="st">'o'</span>, color<span class="op">=</span><span class="st">'w'</span>, markerfacecolor<span class="op">=</span>colors[i], </span>
<span id="cb32-48"><a href="#cb32-48" aria-hidden="true" tabindex="-1"></a>                             markersize<span class="op">=</span><span class="dv">8</span>, label<span class="op">=</span>cat.title()) </span>
<span id="cb32-49"><a href="#cb32-49" aria-hidden="true" tabindex="-1"></a>                  <span class="cf">for</span> i, cat <span class="kw">in</span> <span class="bu">enumerate</span>(word_categories.keys())]</span>
<span id="cb32-50"><a href="#cb32-50" aria-hidden="true" tabindex="-1"></a>fig.legend(handles<span class="op">=</span>legend_elements, loc<span class="op">=</span><span class="st">'upper center'</span>, bbox_to_anchor<span class="op">=</span>(<span class="fl">0.5</span>, <span class="fl">0.02</span>), ncol<span class="op">=</span><span class="dv">6</span>)</span>
<span id="cb32-51"><a href="#cb32-51" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb32-52"><a href="#cb32-52" aria-hidden="true" tabindex="-1"></a>plt.suptitle(<span class="st">'Word Embedding Clustering - t-SNE Analysis'</span>, fontsize<span class="op">=</span><span class="dv">16</span>, fontweight<span class="op">=</span><span class="st">'bold'</span>)</span>
<span id="cb32-53"><a href="#cb32-53" aria-hidden="true" tabindex="-1"></a>plt.tight_layout()</span>
<span id="cb32-54"><a href="#cb32-54" aria-hidden="true" tabindex="-1"></a>plt.show()</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-display">
<div>
<figure class="figure">
<p><img src="inspect_embeddings_files/figure-html/cell-18-output-1.png" class="img-fluid figure-img"></p>
</figure>
</div>
</div>
</div>
</section>

</main>
<!-- /main column -->
<script id="quarto-html-after-body" type="application/javascript">
  window.document.addEventListener("DOMContentLoaded", function (event) {
    const icon = "";
    const anchorJS = new window.AnchorJS();
    anchorJS.options = {
      placement: 'right',
      icon: icon
    };
    anchorJS.add('.anchored');
    const isCodeAnnotation = (el) => {
      for (const clz of el.classList) {
        if (clz.startsWith('code-annotation-')) {                     
          return true;
        }
      }
      return false;
    }
    const onCopySuccess = function(e) {
      // button target
      const button = e.trigger;
      // don't keep focus
      button.blur();
      // flash "checked"
      button.classList.add('code-copy-button-checked');
      var currentTitle = button.getAttribute("title");
      button.setAttribute("title", "Copied!");
      let tooltip;
      if (window.bootstrap) {
        button.setAttribute("data-bs-toggle", "tooltip");
        button.setAttribute("data-bs-placement", "left");
        button.setAttribute("data-bs-title", "Copied!");
        tooltip = new bootstrap.Tooltip(button, 
          { trigger: "manual", 
            customClass: "code-copy-button-tooltip",
            offset: [0, -8]});
        tooltip.show();    
      }
      setTimeout(function() {
        if (tooltip) {
          tooltip.hide();
          button.removeAttribute("data-bs-title");
          button.removeAttribute("data-bs-toggle");
          button.removeAttribute("data-bs-placement");
        }
        button.setAttribute("title", currentTitle);
        button.classList.remove('code-copy-button-checked');
      }, 1000);
      // clear code selection
      e.clearSelection();
    }
    const getTextToCopy = function(trigger) {
        const codeEl = trigger.previousElementSibling.cloneNode(true);
        for (const childEl of codeEl.children) {
          if (isCodeAnnotation(childEl)) {
            childEl.remove();
          }
        }
        return codeEl.innerText;
    }
    const clipboard = new window.ClipboardJS('.code-copy-button:not([data-in-quarto-modal])', {
      text: getTextToCopy
    });
    clipboard.on('success', onCopySuccess);
    if (window.document.getElementById('quarto-embedded-source-code-modal')) {
      const clipboardModal = new window.ClipboardJS('.code-copy-button[data-in-quarto-modal]', {
        text: getTextToCopy,
        container: window.document.getElementById('quarto-embedded-source-code-modal')
      });
      clipboardModal.on('success', onCopySuccess);
    }
      var localhostRegex = new RegExp(/^(?:http|https):\/\/localhost\:?[0-9]*\//);
      var mailtoRegex = new RegExp(/^mailto:/);
        var filterRegex = new RegExp('/' + window.location.host + '/');
      var isInternal = (href) => {
          return filterRegex.test(href) || localhostRegex.test(href) || mailtoRegex.test(href);
      }
      // Inspect non-navigation links and adorn them if external
     var links = window.document.querySelectorAll('a[href]:not(.nav-link):not(.navbar-brand):not(.toc-action):not(.sidebar-link):not(.sidebar-item-toggle):not(.pagination-link):not(.no-external):not([aria-hidden]):not(.dropdown-item):not(.quarto-navigation-tool):not(.about-link)');
      for (var i=0; i<links.length; i++) {
        const link = links[i];
        if (!isInternal(link.href)) {
          // undo the damage that might have been done by quarto-nav.js in the case of
          // links that we want to consider external
          if (link.dataset.originalHref !== undefined) {
            link.href = link.dataset.originalHref;
          }
        }
      }
    function tippyHover(el, contentFn, onTriggerFn, onUntriggerFn) {
      const config = {
        allowHTML: true,
        maxWidth: 500,
        delay: 100,
        arrow: false,
        appendTo: function(el) {
            return el.parentElement;
        },
        interactive: true,
        interactiveBorder: 10,
        theme: 'quarto',
        placement: 'bottom-start',
      };
      if (contentFn) {
        config.content = contentFn;
      }
      if (onTriggerFn) {
        config.onTrigger = onTriggerFn;
      }
      if (onUntriggerFn) {
        config.onUntrigger = onUntriggerFn;
      }
      window.tippy(el, config); 
    }
    const noterefs = window.document.querySelectorAll('a[role="doc-noteref"]');
    for (var i=0; i<noterefs.length; i++) {
      const ref = noterefs[i];
      tippyHover(ref, function() {
        // use id or data attribute instead here
        let href = ref.getAttribute('data-footnote-href') || ref.getAttribute('href');
        try { href = new URL(href).hash; } catch {}
        const id = href.replace(/^#\/?/, "");
        const note = window.document.getElementById(id);
        if (note) {
          return note.innerHTML;
        } else {
          return "";
        }
      });
    }
    const xrefs = window.document.querySelectorAll('a.quarto-xref');
    const processXRef = (id, note) => {
      // Strip column container classes
      const stripColumnClz = (el) => {
        el.classList.remove("page-full", "page-columns");
        if (el.children) {
          for (const child of el.children) {
            stripColumnClz(child);
          }
        }
      }
      stripColumnClz(note)
      if (id === null || id.startsWith('sec-')) {
        // Special case sections, only their first couple elements
        const container = document.createElement("div");
        if (note.children && note.children.length > 2) {
          container.appendChild(note.children[0].cloneNode(true));
          for (let i = 1; i < note.children.length; i++) {
            const child = note.children[i];
            if (child.tagName === "P" && child.innerText === "") {
              continue;
            } else {
              container.appendChild(child.cloneNode(true));
              break;
            }
          }
          if (window.Quarto?.typesetMath) {
            window.Quarto.typesetMath(container);
          }
          return container.innerHTML
        } else {
          if (window.Quarto?.typesetMath) {
            window.Quarto.typesetMath(note);
          }
          return note.innerHTML;
        }
      } else {
        // Remove any anchor links if they are present
        const anchorLink = note.querySelector('a.anchorjs-link');
        if (anchorLink) {
          anchorLink.remove();
        }
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(note);
        }
        if (note.classList.contains("callout")) {
          return note.outerHTML;
        } else {
          return note.innerHTML;
        }
      }
    }
    for (var i=0; i<xrefs.length; i++) {
      const xref = xrefs[i];
      tippyHover(xref, undefined, function(instance) {
        instance.disable();
        let url = xref.getAttribute('href');
        let hash = undefined; 
        if (url.startsWith('#')) {
          hash = url;
        } else {
          try { hash = new URL(url).hash; } catch {}
        }
        if (hash) {
          const id = hash.replace(/^#\/?/, "");
          const note = window.document.getElementById(id);
          if (note !== null) {
            try {
              const html = processXRef(id, note.cloneNode(true));
              instance.setContent(html);
            } finally {
              instance.enable();
              instance.show();
            }
          } else {
            // See if we can fetch this
            fetch(url.split('#')[0])
            .then(res => res.text())
            .then(html => {
              const parser = new DOMParser();
              const htmlDoc = parser.parseFromString(html, "text/html");
              const note = htmlDoc.getElementById(id);
              if (note !== null) {
                const html = processXRef(id, note);
                instance.setContent(html);
              } 
            }).finally(() => {
              instance.enable();
              instance.show();
            });
          }
        } else {
          // See if we can fetch a full url (with no hash to target)
          // This is a special case and we should probably do some content thinning / targeting
          fetch(url)
          .then(res => res.text())
          .then(html => {
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(html, "text/html");
            const note = htmlDoc.querySelector('main.content');
            if (note !== null) {
              // This should only happen for chapter cross references
              // (since there is no id in the URL)
              // remove the first header
              if (note.children.length > 0 && note.children[0].tagName === "HEADER") {
                note.children[0].remove();
              }
              const html = processXRef(null, note);
              instance.setContent(html);
            } 
          }).finally(() => {
            instance.enable();
            instance.show();
          });
        }
      }, function(instance) {
      });
    }
        let selectedAnnoteEl;
        const selectorForAnnotation = ( cell, annotation) => {
          let cellAttr = 'data-code-cell="' + cell + '"';
          let lineAttr = 'data-code-annotation="' +  annotation + '"';
          const selector = 'span[' + cellAttr + '][' + lineAttr + ']';
          return selector;
        }
        const selectCodeLines = (annoteEl) => {
          const doc = window.document;
          const targetCell = annoteEl.getAttribute("data-target-cell");
          const targetAnnotation = annoteEl.getAttribute("data-target-annotation");
          const annoteSpan = window.document.querySelector(selectorForAnnotation(targetCell, targetAnnotation));
          const lines = annoteSpan.getAttribute("data-code-lines").split(",");
          const lineIds = lines.map((line) => {
            return targetCell + "-" + line;
          })
          let top = null;
          let height = null;
          let parent = null;
          if (lineIds.length > 0) {
              //compute the position of the single el (top and bottom and make a div)
              const el = window.document.getElementById(lineIds[0]);
              top = el.offsetTop;
              height = el.offsetHeight;
              parent = el.parentElement.parentElement;
            if (lineIds.length > 1) {
              const lastEl = window.document.getElementById(lineIds[lineIds.length - 1]);
              const bottom = lastEl.offsetTop + lastEl.offsetHeight;
              height = bottom - top;
            }
            if (top !== null && height !== null && parent !== null) {
              // cook up a div (if necessary) and position it 
              let div = window.document.getElementById("code-annotation-line-highlight");
              if (div === null) {
                div = window.document.createElement("div");
                div.setAttribute("id", "code-annotation-line-highlight");
                div.style.position = 'absolute';
                parent.appendChild(div);
              }
              div.style.top = top - 2 + "px";
              div.style.height = height + 4 + "px";
              div.style.left = 0;
              let gutterDiv = window.document.getElementById("code-annotation-line-highlight-gutter");
              if (gutterDiv === null) {
                gutterDiv = window.document.createElement("div");
                gutterDiv.setAttribute("id", "code-annotation-line-highlight-gutter");
                gutterDiv.style.position = 'absolute';
                const codeCell = window.document.getElementById(targetCell);
                const gutter = codeCell.querySelector('.code-annotation-gutter');
                gutter.appendChild(gutterDiv);
              }
              gutterDiv.style.top = top - 2 + "px";
              gutterDiv.style.height = height + 4 + "px";
            }
            selectedAnnoteEl = annoteEl;
          }
        };
        const unselectCodeLines = () => {
          const elementsIds = ["code-annotation-line-highlight", "code-annotation-line-highlight-gutter"];
          elementsIds.forEach((elId) => {
            const div = window.document.getElementById(elId);
            if (div) {
              div.remove();
            }
          });
          selectedAnnoteEl = undefined;
        };
          // Handle positioning of the toggle
      window.addEventListener(
        "resize",
        throttle(() => {
          elRect = undefined;
          if (selectedAnnoteEl) {
            selectCodeLines(selectedAnnoteEl);
          }
        }, 10)
      );
      function throttle(fn, ms) {
      let throttle = false;
      let timer;
        return (...args) => {
          if(!throttle) { // first call gets through
              fn.apply(this, args);
              throttle = true;
          } else { // all the others get throttled
              if(timer) clearTimeout(timer); // cancel #2
              timer = setTimeout(() => {
                fn.apply(this, args);
                timer = throttle = false;
              }, ms);
          }
        };
      }
        // Attach click handler to the DT
        const annoteDls = window.document.querySelectorAll('dt[data-target-cell]');
        for (const annoteDlNode of annoteDls) {
          annoteDlNode.addEventListener('click', (event) => {
            const clickedEl = event.target;
            if (clickedEl !== selectedAnnoteEl) {
              unselectCodeLines();
              const activeEl = window.document.querySelector('dt[data-target-cell].code-annotation-active');
              if (activeEl) {
                activeEl.classList.remove('code-annotation-active');
              }
              selectCodeLines(clickedEl);
              clickedEl.classList.add('code-annotation-active');
            } else {
              // Unselect the line
              unselectCodeLines();
              clickedEl.classList.remove('code-annotation-active');
            }
          });
        }
    const findCites = (el) => {
      const parentEl = el.parentElement;
      if (parentEl) {
        const cites = parentEl.dataset.cites;
        if (cites) {
          return {
            el,
            cites: cites.split(' ')
          };
        } else {
          return findCites(el.parentElement)
        }
      } else {
        return undefined;
      }
    };
    var bibliorefs = window.document.querySelectorAll('a[role="doc-biblioref"]');
    for (var i=0; i<bibliorefs.length; i++) {
      const ref = bibliorefs[i];
      const citeInfo = findCites(ref);
      if (citeInfo) {
        tippyHover(citeInfo.el, function() {
          var popup = window.document.createElement('div');
          citeInfo.cites.forEach(function(cite) {
            var citeDiv = window.document.createElement('div');
            citeDiv.classList.add('hanging-indent');
            citeDiv.classList.add('csl-entry');
            var biblioDiv = window.document.getElementById('ref-' + cite);
            if (biblioDiv) {
              citeDiv.innerHTML = biblioDiv.innerHTML;
            }
            popup.appendChild(citeDiv);
          });
          return popup.innerHTML;
        });
      }
    }
  });
  </script>
</div> <!-- /content -->




</body></html>