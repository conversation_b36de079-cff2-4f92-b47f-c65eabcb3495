from datasets import load_dataset

# Load the TinyStoriesInstruct dataset
print("Loading dataset 'roneneldan/TinyStoriesInstruct'...")
dataset = load_dataset("roneneldan/TinyStoriesInstruct")

# Display available splits
print("Available splits:", dataset.keys())

# Display the number of samples in each split
print("Train split size:", len(dataset["train"]))
print("Validation split size:", len(dataset["validation"]))

# Display a sample from each split
print("\nSample from train split:")
print(dataset["train"][0])

print("\nSample from validation split:")
print(dataset["validation"][0])
