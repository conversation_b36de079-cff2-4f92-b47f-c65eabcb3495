import sys
from bpe_tokenizer import BPETokenizer

# Usage: python tokenize_sentence.py <tokenizer_pickle> <sentence>

def main():
    if len(sys.argv) < 3:
        print("Usage: python tokenize_sentence.py <tokenizer_pickle> <sentence>")
        sys.exit(1)
    tokenizer_pickle = sys.argv[1]
    sentence = ' '.join(sys.argv[2:])

    tokenizer = BPETokenizer.load(tokenizer_pickle)
    token_ids = tokenizer.encode(sentence)
    tokens = [tokenizer.id2token.get(i, '<unk>') for i in token_ids]

    print("Token IDs:", token_ids)
    print("Tokens:", tokens)
    print("Decoded:", tokenizer.decode(token_ids, remove_special_tokens=False))

if __name__ == "__main__":
    main()
