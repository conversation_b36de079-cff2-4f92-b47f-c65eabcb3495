# Data Setup and Preprocessing for Sentiment Analysis
# This code should be used in your train_sentiment_mlp.ipynb and train_sentiment_lstm.ipynb notebooks

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from datasets import load_dataset
from sklearn.model_selection import train_test_split
from sklearn.metrics import f1_score, confusion_matrix, classification_report
from collections import Counter
import matplotlib.pyplot as plt
import seaborn as sns

# Set random seeds for reproducibility
torch.manual_seed(42)
np.random.seed(42)

print("========== Loading Dataset ==========")

# Load the Financial PhraseBank dataset
dataset = load_dataset('financial_phrasebank', 'sentences_50agree', trust_remote_code=True)
print("Dataset loaded successfully!")
print("Example entries:", dataset['train'][:5])

# Extract sentences and labels
sentences = dataset['train']['sentence']
labels = dataset['train']['label']

print(f"\nTotal samples: {len(sentences)}")
print(f"Sample sentence: '{sentences[0]}'")
print(f"Sample label: {labels[0]}")

# Create label mapping
label_names = ['negative', 'neutral', 'positive']  # Based on dataset documentation
label_to_idx = {name: idx for idx, name in enumerate(label_names)}
idx_to_label = {idx: name for name, idx in label_to_idx.items()}

print(f"\nLabel mapping: {label_to_idx}")

# Analyze class distribution
label_counts = Counter(labels)
print(f"\nClass distribution:")
for label_idx, count in sorted(label_counts.items()):
    label_name = idx_to_label[label_idx]
    percentage = (count / len(labels)) * 100
    print(f"  {label_name.capitalize()}: {count} ({percentage:.1f}%)")

# Compute class weights for handling imbalance
# Class weights = 1 / (class_frequency * num_classes)
total_samples = len(labels)
num_classes = len(label_counts)
class_weights = []

for class_idx in range(num_classes):
    class_count = label_counts[class_idx]
    weight = total_samples / (num_classes * class_count)
    class_weights.append(weight)

class_weights = torch.FloatTensor(class_weights)
print(f"\nComputed class weights: {class_weights}")
print("These weights will be used in nn.CrossEntropyLoss(weight=class_weights)")

# Convert to numpy arrays for splitting
X = np.array(sentences)
y = np.array(labels)

print(f"\nData shapes - X: {X.shape}, y: {y.shape}")

# Stratified train/validation/test split
print("\n========== Creating Train/Val/Test Splits ==========")

# First split: separate test set (15%)
X_trainval, X_test, y_trainval, y_test = train_test_split(
    X, y, test_size=0.15, stratify=y, random_state=42
)

# Second split: separate train and validation (15% of remaining for val)
X_train, X_val, y_train, y_val = train_test_split(
    X_trainval, y_trainval, test_size=0.15, stratify=y_trainval, random_state=42
)

print(f"Train set: {len(X_train)} samples")
print(f"Validation set: {len(X_val)} samples") 
print(f"Test set: {len(X_test)} samples")

# Verify stratification worked
def print_split_distribution(y_split, split_name):
    split_counts = Counter(y_split)
    print(f"\n{split_name} distribution:")
    for label_idx in range(num_classes):
        count = split_counts[label_idx]
        percentage = (count / len(y_split)) * 100
        label_name = idx_to_label[label_idx]
        print(f"  {label_name.capitalize()}: {count} ({percentage:.1f}%)")

print_split_distribution(y_train, "Train")
print_split_distribution(y_val, "Validation")
print_split_distribution(y_test, "Test")

# Visualization of class distribution
plt.figure(figsize=(12, 4))

# Overall distribution
plt.subplot(1, 2, 1)
labels_names = [idx_to_label[i].capitalize() for i in range(num_classes)]
counts = [label_counts[i] for i in range(num_classes)]
plt.bar(labels_names, counts, color=['red', 'gray', 'green'])
plt.title('Overall Class Distribution')
plt.ylabel('Count')
for i, count in enumerate(counts):
    plt.text(i, count + 20, str(count), ha='center')

# Class weights visualization
plt.subplot(1, 2, 2)
plt.bar(labels_names, class_weights.numpy(), color=['red', 'gray', 'green'])
plt.title('Computed Class Weights')
plt.ylabel('Weight')
for i, weight in enumerate(class_weights):
    plt.text(i, weight + 0.05, f'{weight:.3f}', ha='center')

plt.tight_layout()
plt.show()

print("\n========== Setup Complete ==========")
print("Next steps:")
print("1. For MLP: Compute mean-pooled FastText embeddings")
print("2. For LSTM: Tokenize sentences and create padded sequences") 
print("3. Use class_weights in nn.CrossEntropyLoss for both models")
print("4. Train for at least 30 epochs and track metrics")

# Helper function for computing macro F1 score
def compute_macro_f1(y_true, y_pred):
    """Compute macro-averaged F1 score"""
    return f1_score(y_true, y_pred, average='macro')

# Helper function for plotting training curves
def plot_training_curves(train_losses, val_losses, train_accs, val_accs, train_f1s, val_f1s):
    """Plot training and validation curves"""
    epochs = range(1, len(train_losses) + 1)
    
    fig, axes = plt.subplots(1, 3, figsize=(15, 4))
    
    # Loss
    axes[0].plot(epochs, train_losses, 'b-', label='Train')
    axes[0].plot(epochs, val_losses, 'r-', label='Validation')
    axes[0].set_title('Loss vs Epochs')
    axes[0].set_xlabel('Epochs')
    axes[0].set_ylabel('Loss')
    axes[0].legend()
    axes[0].grid(True)
    
    # Accuracy
    axes[1].plot(epochs, train_accs, 'b-', label='Train')
    axes[1].plot(epochs, val_accs, 'r-', label='Validation')
    axes[1].set_title('Accuracy vs Epochs')
    axes[1].set_xlabel('Epochs')
    axes[1].set_ylabel('Accuracy')
    axes[1].legend()
    axes[1].grid(True)
    
    # Macro F1
    axes[2].plot(epochs, train_f1s, 'b-', label='Train')
    axes[2].plot(epochs, val_f1s, 'r-', label='Validation')
    axes[2].set_title('Macro F1 Score vs Epochs')
    axes[2].set_xlabel('Epochs')
    axes[2].set_ylabel('Macro F1 Score')
    axes[2].legend()
    axes[2].grid(True)
    
    plt.tight_layout()
    plt.show()

print("\nUtility functions defined:")
print("- compute_macro_f1(y_true, y_pred)")
print("- plot_training_curves(train_losses, val_losses, train_accs, val_accs, train_f1s, val_f1s)")
