.env
dagster_home/*
!dagster.yaml
.vscode/
.pytest_cache/
.coverage*
__pycache__/
.ipynb_checkpoints/
.venv/
.idea/
dc_ecs_template.yml
.DS_store
.secrets
**.csv
.env
*.tar
*.db
*.db-journal
sandbox*
!sample_sandbox.ipynb
!load_dagster_assets_example.ipynb
.prettierignore
*.xlsx
sql_sandbox/*
.ruff_cache/

**/tuned_embedder/
**/*.zip

# from `cdk init`
*.swp
package-lock.json
__pycache__
.pytest_cache
.venv
*.egg-info
.testmondata*

# CDK asset staging directory
.cdk.staging
cdk.out
cdk.context.json
dotenv.json
cdk.out/

**/dbt_source/target/
**/dbt_source/logs/
.user.yml
*dbt.log*


**/tmp*


*.pkl
*.pt
*.pth
*.onnx
*.npy