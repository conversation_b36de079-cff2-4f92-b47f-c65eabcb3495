{"cells": [{"cell_type": "raw", "id": "6af9dccb", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "title: \"Assignment 2: Implement and Compare Word Embeddings\"\n", "format: \n", "  html:\n", "    toc: true\n", "    toc-title: Contents\n", "    toc-depth: 4\n", "    self-contained: true\n", "    number-sections: false\n", "jupyter: python3\n", "---"]}, {"cell_type": "markdown", "id": "e363b71f", "metadata": {}, "source": ["## Objective\n", "\n", "In this assignment, you will:\n", "\n", "- Implement and train a **Word2Vec Skip-Gram model with negative sampling in PyTorch** on **GPU**\n", "- Train a **Gensim Word2Vec model** on the same text corpus\n", "- Use **pretrained embeddings** from Gensim (**Word2Vec Google News** and **FastText**)\n", "- **Compare** the quality of the learned embeddings across the four sources via **sentence similarity analysis**  and **visualization**"]}, {"cell_type": "markdown", "id": "87618830", "metadata": {}, "source": ["## Training dataset\n", "\n", "**Text8** is a small, clean corpus from Wikipedia, commonly used to train and benchmark word embeddings.\n", "\n", "- ~17 million tokens (~100MB)\n", "- Lowercased English words; no punctuation or rare words\n", "- Format: list of tokenized sentences\n"]}, {"cell_type": "markdown", "id": "34d6cbbe", "metadata": {}, "source": ["## Files Provided\n", "\n", "- `data.py`: Preprocesses the `text8` corpus and generates skip-gram pairs.\n", "- `download_gensim_model.py`: Downloads pretrained Word2Vec and FastText models.\n", "- `gensim_train_word2vec.py`: Example to train embeddings using Gensim (for comparison only).\n", "- `pytorch_show_embeddings.py`: Visualization of embeddings trained with PyTorch."]}, {"cell_type": "markdown", "id": "b6279118", "metadata": {}, "source": ["## Your Tasks (20 points)\n", "\n", "### Part 1: Data Preparation\n", "\n", "Run `data.py` to:\n", "\n", "- Download and tokenize the `text8` corpus\n", "- Generate skip-gram pairs using a window size of 2\n", "- Save the processed dataset as `processed_data.pkl`"]}, {"cell_type": "markdown", "id": "066b7566", "metadata": {}, "source": ["### Part 2: Implement Word2Vec with Py<PERSON>or<PERSON>  (10 points)\n", "\n", "In the file named `pytorch_train_word2vec.py`, implement the **Skip-Gram with Negative Sampling** model using PyTorch.\n", "\n", "Use the following **hyperparameters** exactly:\n", "\n", "```python\n", "# Hyperparameters\n", "EMBEDDING_DIM = 100\n", "BATCH_SIZE = 128\n", "EPOCHS = 25\n", "LEARNING_RATE = 0.01\n", "NEGATIVE_SAMPLES = 5  # Number of negative samples per positive pair\n", "```"]}, {"cell_type": "markdown", "id": "6dcd3e96", "metadata": {}, "source": ["#### What to implement \n", "\n", "1. `SkipGramDataset`  \n", "Use the center-context skip-gram pairs generated in `processed_data.pkl` to define a custom `Dataset` class.\n", "\n", "2. `Word2Vec` Model  \n", "Implement the model with two `nn.Embedding` layers:\n", "- One for input (center words)  \n", "- One for output (context words)  \n", "\n", "The forward pass should return the dot product between the center and context embeddings.\n", "\n", "3. Negative Sampling Strategy \n", "   \n", "Implement **negative sampling** using the **3/4 power of the unigram distribution**, as used in the original Word2Vec paper. You will compute the negative sampling distribution from word frequency counts as follows:\n", "\n", "```\n", "# Step 1: Retrieve or build word frequencies\n", "word_freq = data['counter'] if 'counter' in data else None\n", "if word_freq is None:\n", "    import pandas as pd\n", "    word_freq = pd.Series(skipgram_df['center'].tolist() + skipgram_df['context'].tolist()).value_counts().to_dict()\n", "\n", "# Step 2: Build frequency-aligned tensor\n", "word_counts = torch.tensor([\n", "    word_freq[data['idx2word'][i]] if data['idx2word'][i] in word_freq else 1\n", "    for i in range(vocab_size)\n", "], dtype=torch.float)\n", "\n", "# Step 3: Apply 3/4 power smoothing\n", "unigram_dist = word_counts ** 0.75\n", "neg_sampling_dist = unigram_dist / unigram_dist.sum()\n", "\n", "```"]}, {"cell_type": "markdown", "id": "f508c324", "metadata": {}, "source": ["Use `torch.multinomial()` to draw negative samples:\n", "\n", "- For each positive pair, draw `NEGATIVE_SAMPLES = 5` negative context words.\n", "- Use `BCEWithLogitsLoss` to compute loss:\n", "  - Label = `1` for positive pairs\n", "  - Label = `0` for negative pairs"]}, {"cell_type": "markdown", "id": "eb74f205", "metadata": {}, "source": ["4. Training Loop\n", "\n", "- Shuffle data using `DataLoader`\n", "- Move data to **GPU**\n", "- For each batch:\n", "  - Compute positive sample loss\n", "  - Sample and compute negative sample loss\n", "  - Combine and backpropagate"]}, {"cell_type": "markdown", "id": "966488e2", "metadata": {}, "source": ["5. Save Trained Embeddings\n", "\n", "After training, save your model’s input embedding weights to disk as a pickle file:\n", "\n", "```python\n", "with open('word2vec_embeddings.pkl', 'wb') as f:\n", "    pickle.dump({'embeddings': embeddings, 'word2idx': data['word2idx'], 'idx2word': data['idx2word']}, f)\n", "print(\"Embeddings saved to word2vec_embeddings.pkl\")\n", "```\n", "If the code runs successfully:\n", "\n", "1. It **Creates a file** named `'word2vec_embeddings.pkl'` in the current directory (or path provided).\n", "\n", "2. It **Saves a dictionary** into that file using Python's `pickle` module. The dictionary includes:\n", "\n", "   - `'embeddings'`: A NumPy array or tensor of your trained word vectors.\n", "   - `'word2idx'`: A dictionary mapping each word to its index in the embeddings matrix.\n", "   - `'idx2word'`: A reverse dictionary mapping each index back to its word.\n", "\n", "\n", "To use the saved embeddings later (e.g., for evaluation, visualization, or similarity lookup), load them back with:\n", "\n", "```\n", "import pickle\n", "\n", "with open('word2vec_embeddings.pkl', 'rb') as f:\n", "    data = pickle.load(f)\n", "\n", "embeddings = data['embeddings']\n", "word2idx = data['word2idx']\n", "idx2word = data['idx2word']\n", "```"]}, {"cell_type": "markdown", "id": "ccba31d9", "metadata": {}, "source": ["### Part 3: Verify Your PyTorch Embeddings\n", "\n", "After training your PyTorch Word2Vec model and saving the embeddings to `word2vec_embeddings.pkl`, run the `pytorch_show_embeddings.py` script to **confirm the embeddings were saved and loaded correctly**\n"]}, {"cell_type": "markdown", "id": "15fe9771", "metadata": {}, "source": ["###  Part 4: Gensim Word2Vec (Provided for Comparison)\n", "\n", "- Run `gensim_train_word2vec.py` to train a Gensim Word2Vec model on `text8`\n", "- It will save:\n", "  - `word2vec_text8_gensim.model`\n", "  - `word2vec_gensim_embeddings.pkl`\n"]}, {"cell_type": "markdown", "id": "94eb9f3b", "metadata": {}, "source": ["### Part 5: Pretrained Embeddings (Gensim)\n", "\n", "- Run `download_gensim_model.py` to download the pretrained models provided by Gensim:\n", "  - `word2vec-google-news-300`: Word2Vec (300-dimensional vectors trained on Google News)\n", "  - `fasttext-wiki-news-subwords-300`: FastText (300-dimensional subword-based vectors trained on Wikipedia)\n", "\n", "After running this script, the following model files will be saved locally:\n", "\n", "- `word2vec-google-news-300.model`\n", "- `fasttext-wiki-news-subwords-300.model`\n"]}, {"cell_type": "markdown", "id": "c370e4c4", "metadata": {}, "source": ["### Part 6:  Inspecting and comparing embeddings (8 points)\n", "\n", "So far, you have obtained four sets of word embeddings for comparison:\n", "\n", "| **Source**                   | **Trained By**      | **Dim** | **Notes**                                 |\n", "|-----------------------------|---------------------|---------|-------------------------------------------|\n", "| PyTorch Skip-Gram           | PyTorch             | 100     | Your own implementation on the Text8 corpus |\n", "| Gensim Word2Vec             | Gensim              | 100     | Trained using Gensim's API on the Text8 corpus |\n", "| Gensim Word2Vec (Google)    | Pretrained          | 300     | Trained on Google News                    |\n", "| Gensim FastText             | Pretrained          | 300     | Includes subword information              |\n", "\n", "Next, let’s evaluate the embeddings you trained alongside the pretrained embeddings from Gensim on the following two tasks:\n", "1. **Sentence similarity**\n", "2. **Embedding visualization**\n", "\n", "Create a notebook called `inspect_embeddings.ipynb` for these two tasks."]}, {"cell_type": "markdown", "id": "c62c8c84", "metadata": {}, "source": ["#### Sentence Similarity\n", "\n", "In lecture, we demonstrated how word embeddings can be used to compute the similarity between two words. This idea extends naturally to **sentence similarity** as well.\n", "\n", "To get started, use the following code to tokenize a sentence into words. (We will explore more advanced tokenization methods—like subword tokenizers—when we cover Transformers in detail.)\n", "\n", "```python\n", "import nltk\n", "nltk.download('punkt')\n", "\n", "from nltk.tokenize import word_tokenize\n", "\n", "def tokenize_text(text):\n", "    return word_tokenize(text.lower())\n", "```"]}, {"cell_type": "markdown", "id": "e0de3701", "metadata": {}, "source": ["To compute a sentence embedding, we take the **average (mean)** of the embeddings of all words in the sentence. \n", "\n", "For both the query sentence and each sentence in the list defined below:\n", "- Compute the average of the word embeddings to obtain the sentence embedding.\n", "- Compute and print the **cosine similarity** of the query sentence against each of the sentences in the list.\n", "- Compare how sentence-level meaning is captured across different embedding sources."]}, {"cell_type": "code", "execution_count": 1, "id": "9554c673", "metadata": {}, "outputs": [], "source": ["# List of sentences for comparison\n", "sentences = [\n", "    \"The cat sat on the mat.\",\n", "    \"A dog is playing in the yard.\",\n", "    \"I need to buy groceries today.\",\n", "    \"The feline was resting on the rug.\",\n", "    \"Canines enjoy outdoor activities.\",\n", "    \"My shopping list includes milk and bread.\",\n", "    \"The weather is beautiful today.\",\n", "    \"Programming requires logical thinking.\"\n", "]\n", "\n", "# Query sentence\n", "query = \"My pet cat is sleeping on the carpet.\""]}, {"cell_type": "markdown", "id": "9406c69d", "metadata": {}, "source": ["####  Embedding Visualization\n", "\n", "Use **t-SNE** and **UMAP** to visualize and compare how different embedding sources cluster semantically related words. Specifically, plot the embeddings of the following two semantic categories:\n", "\n", "- Color code each category (e.g., animals in blue, foods in orange)\n", "\n", "- Run visualizations for **all four embedding types**:\n", "  - <PERSON><PERSON><PERSON><PERSON><PERSON>-trained\n", "  - Gen<PERSON>m-trained\n", "  - Pretrained Word2Vec\n", "  - Pretrained FastText\n", "\n", "- Observe whether words from the same category cluster together, and whether the separation between categories is consistent across embeddings.\n", "\n", "> 💡 *Optional:* Add a third category (e.g., `vehicles` or `professions`) to explore clustering behavior further.\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "id": "31883a9e", "metadata": {}, "outputs": [], "source": ["animals = [\n", "    'dog', 'cat', 'horse', 'cow',\n", "    'lion', 'tiger', 'elephant', 'giraffe',\n", "    'eagle', 'hawk', 'sparrow', 'penguin',\n", "    'shark', 'whale', 'dolphin', 'tuna'\n", "]\n", "\n", "foods = [\n", "    'apple', 'orange', 'banana', 'grape',\n", "    'bread', 'pasta', 'rice', 'potato',\n", "    'chicken', 'beef', 'pork', 'fish',\n", "    'coffee', 'tea', 'juice', 'water'\n", "]"]}, {"cell_type": "markdown", "id": "c956abd8", "metadata": {}, "source": ["###  Part 7: Short Answer Questions (2 points)\n", "\n", "After completing the previous tasks, answer the following questions in a short written response (about 1 page):\n", "\n", "- Which embeddings performed best at capturing sentence meaning?\n", "- Were the pretrained embeddings significantly better than your own? Why or why not?\n", "- What factors might explain the differences in performance?\n"]}, {"cell_type": "markdown", "id": "ce80f51a", "metadata": {}, "source": ["### Bonus Question (5 points)\n", "\n", "Think creatively! Propose and implement your own method to **explore or analyze word embeddings** beyond the provided tasks.\n", "\n", "Your idea could include (but is not limited to):\n", "- Word analogy tasks (e.g., *king - man + woman ≈ queen*)\n", "- Clustering words into semantic groups\n", "- Visualizing relationships between parts of speech\n", "- Detecting outliers or unusual word placements\n", "- Creating a mini application or visualization\n", "\n", "Be original — any insightful or fun exploration that demonstrates your understanding of word embeddings will be considered!\n"]}, {"cell_type": "markdown", "id": "4639b992", "metadata": {}, "source": ["## What to Submit\n", "\n", "1. `pytorch_train_word2vec.py`: Your PyTorch implementation of the Word2Vec model.\n", "\n", "2. A notebook `inspect_embeddings.ipynb` that:\n", "   - Loads both trained and pretrained embeddings  \n", "   - Performs sentence similarity analysis  \n", "   - Includes clustering visualizations (e.g., PCA or t-SNE)\n", "\n", "\n", "**Important Notes:**\n", "\n", "- You must **render and submit the HTML version of your notebook using Quarto.**  \n", "  The HTML file should include all outputs (e.g., plots, similarity scores).\n", "  \n", "- Make sure all relevant files (scripts, data, models) are **committed to your repository**, so your code can be run without issues.\n", "\n"]}, {"cell_type": "raw", "id": "bb7568e5", "metadata": {}, "source": ["---\n", "title: \"Assignment 2: Implement and Compare Word Embeddings\"\n", "format: \n", "  html:\n", "    toc: true\n", "    toc-title: Contents\n", "    toc-depth: 4\n", "    self-contained: true\n", "    number-sections: false\n", "jupyter: python3\n", "---"]}, {"cell_type": "markdown", "id": "2d6ade1c", "metadata": {}, "source": ["## Objective\n", "\n", "In this assignment, you will:\n", "\n", "- Implement and train a **Word2Vec Skip-Gram model with negative sampling in PyTorch** on **GPU**\n", "- Train a **Gensim Word2Vec model** on the same text corpus\n", "- Use **pretrained embeddings** from Gensim (**Word2Vec Google News** and **FastText**)\n", "- **Compare** the quality of the learned embeddings across the four sources via **sentence similarity analysis**  and **visualization**"]}, {"cell_type": "markdown", "id": "dd709c3a", "metadata": {}, "source": ["## Training dataset\n", "\n", "**Text8** is a small, clean corpus from Wikipedia, commonly used to train and benchmark word embeddings.\n", "\n", "- ~17 million tokens (~100MB)\n", "- Lowercased English words; no punctuation or rare words\n", "- Format: list of tokenized sentences\n"]}, {"cell_type": "markdown", "id": "88ff486b", "metadata": {}, "source": ["## Files Provided\n", "\n", "- `data.py`: Preprocesses the `text8` corpus and generates skip-gram pairs.\n", "- `download_gensim_model.py`: Downloads pretrained Word2Vec and FastText models.\n", "- `gensim_train_word2vec.py`: Example to train embeddings using Gensim (for comparison only).\n", "- `pytorch_show_embeddings.py`: Visualization of embeddings trained with PyTorch."]}, {"cell_type": "markdown", "id": "c1056dc0", "metadata": {}, "source": ["## Your Tasks (20 points)\n", "\n", "### Part 1: Data Preparation\n", "\n", "Run `data.py` to:\n", "\n", "- Download and tokenize the `text8` corpus\n", "- Generate skip-gram pairs using a window size of 2\n", "- Save the processed dataset as `processed_data.pkl`"]}, {"cell_type": "markdown", "id": "f8079d1c", "metadata": {}, "source": ["### Part 2: Implement Word2Vec with Py<PERSON>or<PERSON>  (10 points)\n", "\n", "In the file named `pytorch_train_word2vec.py`, implement the **Skip-Gram with Negative Sampling** model using PyTorch.\n", "\n", "Use the following **hyperparameters** exactly:\n", "\n", "```python\n", "# Hyperparameters\n", "EMBEDDING_DIM = 100\n", "BATCH_SIZE = 128\n", "EPOCHS = 25\n", "LEARNING_RATE = 0.01\n", "NEGATIVE_SAMPLES = 5  # Number of negative samples per positive pair\n", "```"]}, {"cell_type": "markdown", "id": "3ed8d03c", "metadata": {}, "source": ["#### What to implement \n", "\n", "1. `SkipGramDataset`  \n", "Use the center-context skip-gram pairs generated in `processed_data.pkl` to define a custom `Dataset` class.\n", "\n", "2. `Word2Vec` Model  \n", "Implement the model with two `nn.Embedding` layers:\n", "- One for input (center words)  \n", "- One for output (context words)  \n", "\n", "The forward pass should return the dot product between the center and context embeddings.\n", "\n", "3. Negative Sampling Strategy \n", "   \n", "Implement **negative sampling** using the **3/4 power of the unigram distribution**, as used in the original Word2Vec paper. You will compute the negative sampling distribution from word frequency counts as follows:\n", "\n", "```\n", "# Step 1: Retrieve or build word frequencies\n", "word_freq = data['counter'] if 'counter' in data else None\n", "if word_freq is None:\n", "    import pandas as pd\n", "    word_freq = pd.Series(skipgram_df['center'].tolist() + skipgram_df['context'].tolist()).value_counts().to_dict()\n", "\n", "# Step 2: Build frequency-aligned tensor\n", "word_counts = torch.tensor([\n", "    word_freq[data['idx2word'][i]] if data['idx2word'][i] in word_freq else 1\n", "    for i in range(vocab_size)\n", "], dtype=torch.float)\n", "\n", "# Step 3: Apply 3/4 power smoothing\n", "unigram_dist = word_counts ** 0.75\n", "neg_sampling_dist = unigram_dist / unigram_dist.sum()\n", "\n", "```"]}, {"cell_type": "markdown", "id": "8e3fc456", "metadata": {}, "source": ["Use `torch.multinomial()` to draw negative samples:\n", "\n", "- For each positive pair, draw `NEGATIVE_SAMPLES = 5` negative context words.\n", "- Use `BCEWithLogitsLoss` to compute loss:\n", "  - Label = `1` for positive pairs\n", "  - Label = `0` for negative pairs"]}, {"cell_type": "markdown", "id": "5b05641c", "metadata": {}, "source": ["4. Training Loop\n", "\n", "- Shuffle data using `DataLoader`\n", "- Move data to **GPU**\n", "- For each batch:\n", "  - Compute positive sample loss\n", "  - Sample and compute negative sample loss\n", "  - Combine and backpropagate"]}, {"cell_type": "markdown", "id": "f7548213", "metadata": {}, "source": ["5. Save Trained Embeddings\n", "\n", "After training, save your model’s input embedding weights to disk as a pickle file:\n", "\n", "```python\n", "with open('word2vec_embeddings.pkl', 'wb') as f:\n", "    pickle.dump({'embeddings': embeddings, 'word2idx': data['word2idx'], 'idx2word': data['idx2word']}, f)\n", "print(\"Embeddings saved to word2vec_embeddings.pkl\")\n", "```\n", "If the code runs successfully:\n", "\n", "1. It **Creates a file** named `'word2vec_embeddings.pkl'` in the current directory (or path provided).\n", "\n", "2. It **Saves a dictionary** into that file using Python's `pickle` module. The dictionary includes:\n", "\n", "   - `'embeddings'`: A NumPy array or tensor of your trained word vectors.\n", "   - `'word2idx'`: A dictionary mapping each word to its index in the embeddings matrix.\n", "   - `'idx2word'`: A reverse dictionary mapping each index back to its word.\n", "\n", "\n", "To use the saved embeddings later (e.g., for evaluation, visualization, or similarity lookup), load them back with:\n", "\n", "```\n", "import pickle\n", "\n", "with open('word2vec_embeddings.pkl', 'rb') as f:\n", "    data = pickle.load(f)\n", "\n", "embeddings = data['embeddings']\n", "word2idx = data['word2idx']\n", "idx2word = data['idx2word']\n", "```"]}, {"cell_type": "markdown", "id": "798b3d97", "metadata": {}, "source": ["### Part 3: Verify Your PyTorch Embeddings\n", "\n", "After training your PyTorch Word2Vec model and saving the embeddings to `word2vec_embeddings.pkl`, run the `pytorch_show_embeddings.py` script to **confirm the embeddings were saved and loaded correctly**\n"]}, {"cell_type": "markdown", "id": "82ca5763", "metadata": {}, "source": ["###  Part 4: Gensim Word2Vec (Provided for Comparison)\n", "\n", "- Run `gensim_train_word2vec.py` to train a Gensim Word2Vec model on `text8`\n", "- It will save:\n", "  - `word2vec_text8_gensim.model`\n", "  - `word2vec_gensim_embeddings.pkl`\n"]}, {"cell_type": "markdown", "id": "703b39c9", "metadata": {}, "source": ["### Part 5: Pretrained Embeddings (Gensim)\n", "\n", "- Run `download_gensim_model.py` to download the pretrained models provided by Gensim:\n", "  - `word2vec-google-news-300`: Word2Vec (300-dimensional vectors trained on Google News)\n", "  - `fasttext-wiki-news-subwords-300`: FastText (300-dimensional subword-based vectors trained on Wikipedia)\n", "\n", "After running this script, the following model files will be saved locally:\n", "\n", "- `word2vec-google-news-300.model`\n", "- `fasttext-wiki-news-subwords-300.model`\n"]}, {"cell_type": "markdown", "id": "a44a0322", "metadata": {}, "source": ["### Part 6:  Inspecting and comparing embeddings (8 points)\n", "\n", "So far, you have obtained four sets of word embeddings for comparison:\n", "\n", "| **Source**                   | **Trained By**      | **Dim** | **Notes**                                 |\n", "|-----------------------------|---------------------|---------|-------------------------------------------|\n", "| PyTorch Skip-Gram           | PyTorch             | 100     | Your own implementation on the Text8 corpus |\n", "| Gensim Word2Vec             | Gensim              | 100     | Trained using Gensim's API on the Text8 corpus |\n", "| Gensim Word2Vec (Google)    | Pretrained          | 300     | Trained on Google News                    |\n", "| Gensim FastText             | Pretrained          | 300     | Includes subword information              |\n", "\n", "Next, let’s evaluate the embeddings you trained alongside the pretrained embeddings from Gensim on the following two tasks:\n", "1. **Sentence similarity**\n", "2. **Embedding visualization**\n", "\n", "Create a notebook called `inspect_embeddings.ipynb` for these two tasks."]}, {"cell_type": "markdown", "id": "c3078fe5", "metadata": {}, "source": ["#### Sentence Similarity\n", "\n", "In lecture, we demonstrated how word embeddings can be used to compute the similarity between two words. This idea extends naturally to **sentence similarity** as well.\n", "\n", "To get started, use the following code to tokenize a sentence into words. (We will explore more advanced tokenization methods—like subword tokenizers—when we cover Transformers in detail.)\n", "\n", "```python\n", "import nltk\n", "nltk.download('punkt')\n", "\n", "from nltk.tokenize import word_tokenize\n", "\n", "def tokenize_text(text):\n", "    return word_tokenize(text.lower())\n", "```"]}, {"cell_type": "markdown", "id": "f93d4599", "metadata": {}, "source": ["To compute a sentence embedding, we take the **average (mean)** of the embeddings of all words in the sentence. \n", "\n", "For both the query sentence and each sentence in the list defined below:\n", "- Compute the average of the word embeddings to obtain the sentence embedding.\n", "- Compute and print the **cosine similarity** of the query sentence against each of the sentences in the list.\n", "- Compare how sentence-level meaning is captured across different embedding sources."]}, {"cell_type": "code", "execution_count": null, "id": "9585352c", "metadata": {}, "outputs": [], "source": ["# List of sentences for comparison\n", "sentences = [\n", "    \"The cat sat on the mat.\",\n", "    \"A dog is playing in the yard.\",\n", "    \"I need to buy groceries today.\",\n", "    \"The feline was resting on the rug.\",\n", "    \"Canines enjoy outdoor activities.\",\n", "    \"My shopping list includes milk and bread.\",\n", "    \"The weather is beautiful today.\",\n", "    \"Programming requires logical thinking.\"\n", "]\n", "\n", "# Query sentence\n", "query = \"My pet cat is sleeping on the carpet.\""]}, {"cell_type": "markdown", "id": "b33691fc", "metadata": {}, "source": ["####  Embedding Visualization\n", "\n", "Use **t-SNE** and **UMAP** to visualize and compare how different embedding sources cluster semantically related words. Specifically, plot the embeddings of the following two semantic categories:\n", "\n", "- Color code each category (e.g., animals in blue, foods in orange)\n", "\n", "- Run visualizations for **all four embedding types**:\n", "  - <PERSON><PERSON><PERSON><PERSON><PERSON>-trained\n", "  - Gen<PERSON>m-trained\n", "  - Pretrained Word2Vec\n", "  - Pretrained FastText\n", "\n", "- Observe whether words from the same category cluster together, and whether the separation between categories is consistent across embeddings.\n", "\n", "> 💡 *Optional:* Add a third category (e.g., `vehicles` or `professions`) to explore clustering behavior further.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d24054bd", "metadata": {}, "outputs": [], "source": ["animals = [\n", "    'dog', 'cat', 'horse', 'cow',\n", "    'lion', 'tiger', 'elephant', 'giraffe',\n", "    'eagle', 'hawk', 'sparrow', 'penguin',\n", "    'shark', 'whale', 'dolphin', 'tuna'\n", "]\n", "\n", "foods = [\n", "    'apple', 'orange', 'banana', 'grape',\n", "    'bread', 'pasta', 'rice', 'potato',\n", "    'chicken', 'beef', 'pork', 'fish',\n", "    'coffee', 'tea', 'juice', 'water'\n", "]"]}, {"cell_type": "markdown", "id": "db703a83", "metadata": {}, "source": ["###  Part 7: Short Answer Questions (2 points)\n", "\n", "After completing the previous tasks, answer the following questions in a short written response (about 1 page):\n", "\n", "- Which embeddings performed best at capturing sentence meaning?\n", "- Were the pretrained embeddings significantly better than your own? Why or why not?\n", "- What factors might explain the differences in performance?\n"]}, {"cell_type": "markdown", "id": "f7a9e2a4", "metadata": {}, "source": ["### Bonus Question (5 points)\n", "\n", "Think creatively! Propose and implement your own method to **explore or analyze word embeddings** beyond the provided tasks.\n", "\n", "Your idea could include (but is not limited to):\n", "- Word analogy tasks (e.g., *king - man + woman ≈ queen*)\n", "- Clustering words into semantic groups\n", "- Visualizing relationships between parts of speech\n", "- Detecting outliers or unusual word placements\n", "- Creating a mini application or visualization\n", "\n", "Be original — any insightful or fun exploration that demonstrates your understanding of word embeddings will be considered!\n"]}, {"cell_type": "markdown", "id": "a1473e97", "metadata": {}, "source": ["## What to Submit\n", "\n", "1. `pytorch_train_word2vec.py`: Your PyTorch implementation of the Word2Vec model.\n", "\n", "2. A notebook `inspect_embeddings.ipynb` that:\n", "   - Loads both trained and pretrained embeddings  \n", "   - Performs sentence similarity analysis  \n", "   - Includes clustering visualizations (e.g., PCA or t-SNE)\n", "\n", "\n", "**Important Notes:**\n", "\n", "- You must **render and submit the HTML version of your notebook using Quarto.**  \n", "  The HTML file should include all outputs (e.g., plots, similarity scores).\n", "  \n", "- Make sure all relevant files (scripts, data, models) are **committed to your repository**, so your code can be run without issues.\n", "\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}