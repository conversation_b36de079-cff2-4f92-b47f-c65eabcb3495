# TinyStories: How Small Can Language Models Be and Still Speak Coherent English?

## Summary

**TinyStories** introduces a synthetic dataset of short stories, designed to be understandable by 3-4 year-old children, generated using GPT-3.5 and GPT-4. The goal is to enable the training and evaluation of very small language models (SLMs)—with fewer than 10 million parameters or even just a single transformer block—that can still generate fluent, consistent, and diverse English stories.

### Model Architecture

- **Base Architecture:** The models are based on the GPT-Neo architecture.
- **Parameter Count:** Models range from 1M to 80M parameters, with a focus on models below 10M.
- **Layers:** Experiments include models with as few as one transformer block (layer), up to 8 layers.
- **Embedding Dimension:** Typically 256, but experiments cover a range of hidden sizes.
- **Attention Heads:** The number of attention heads is varied; increasing heads generally improves performance for a fixed width/depth.
- **Tokenizer:** GPT-Neo tokenizer, restricted to the top 10,000 most common tokens.
- **Context Window:** Window size 256, context length 512.

### Training Procedure

- **Dataset:** TinyStories, a synthetic dataset of short, simple stories, each 2-3 paragraphs, using a vocabulary of about 1,500 basic words.
- **Data Generation:** Stories are generated by prompting GPT-3.5/4 with random combinations of a verb, noun, and adjective, plus optional features (dialogue, plot twist, bad ending, moral value).
- **Instruction-Following Variant:** TinyStories-Instruct (TNI) adds instructions (words to use, sentences to include, features, or summaries) before each story, enabling instruction-following evaluation.
- **Training Resources:** All models can be trained on a single GPU in less than a day (up to 30 hours for the largest).
- **Evaluation:** 
  - A new paradigm uses GPT-4 to grade model outputs on grammar, creativity, consistency, and plot coherence, simulating a human teacher.
  - Models are evaluated on factual knowledge, reasoning, and context-tracking using custom prompts.
  - Out-of-distribution generalization is tested by holding out certain instruction combinations during training.

### Key Findings

- **Emergence of Capabilities:** Even very small models (as low as 2.5M parameters or a single transformer layer) can generate coherent, grammatically correct, and contextually consistent stories, and show some reasoning ability.
- **Scaling Laws:** The dataset allows the study of scaling laws and trade-offs between model width and depth at a much smaller scale than typical LLMs.
- **Interpretability:** Small models are more interpretable; attention heads and MLP neurons often have clear, human-understandable roles (e.g., tracking subjects, actions, or specific words).
- **Diversity:** The models do not simply memorize or template-match the training data; generated stories are diverse and novel, as shown by manual inspection and quantitative n-gram overlap analysis.
- **Instruction Following:** Models trained on TNI can follow complex instructions, even in out-of-distribution settings.

### Conclusion

TinyStories demonstrates that with a carefully designed, restricted dataset, it is possible to train very small language models that can generate coherent, creative, and contextually appropriate English text. The work also introduces a new evaluation paradigm using LLMs as graders, and provides insights into the roles of model width, depth, and attention heads in the emergence of language capabilities.
