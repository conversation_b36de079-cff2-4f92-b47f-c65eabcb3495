"""
<PERSON><PERSON><PERSON> to generate a list of fruits using <PERSON><PERSON><PERSON><PERSON>'s structured output with Ollama Llama model (using langchain_ollama and langchain_core).
"""
from langchain_core.prompts import ChatPromptTemplate
from langchain_ollama.llms import OllamaLLM
from langchain_core.output_parsers import JsonOutputParser

# Define the output schema as a JSON structure
output_schema = {
    "fruits": ["str"]
}

# Create a parser for the expected output
parser = JsonOutputParser()

# Prompt template for structured output
prompt = ChatPromptTemplate.from_template(
    """
List 10 different fruits as a JSON list under the key 'fruits'.\nExample: {{\n  \"fruits\": [\"apple\", \"banana\", ...] \n}}\nReturn only the JSON.\n"""
)

# Connect to Ollama Llama model
model = OllamaLLM(model="gemma3")

# Chain prompt and model
chain = prompt | model | parser

# Invoke the chain
result = chain.invoke({})

print("Generated list of fruits:")
for fruit in result['fruits']:
    print(f"- {fruit}")
