import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import gensim.downloader as api
from sklearn.metrics import f1_score, confusion_matrix, classification_report
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import re
from tqdm import tqdm

# Set random seeds
torch.manual_seed(42)
np.random.seed(42)



# Data Setup
from datasets import load_dataset
from sklearn.model_selection import train_test_split

dataset = load_dataset('financial_phrasebank', 'sentences_50agree', trust_remote_code=True)
sentences = dataset['train']['sentence']
labels = dataset['train']['label']

X = np.array(sentences)
y = np.array(labels)

X_trainval, X_test, y_trainval, y_test = train_test_split(
    X, y, test_size=0.15, stratify=y, random_state=42
)
X_train, X_val, y_train, y_val = train_test_split(
    X_trainval, y_trainval, test_size=0.15, stratify=y_trainval, random_state=42
)



# Calculate class weights
label_counts = Counter(y_train)
total_samples = len(y_train)
num_classes = 3
class_weights = torch.FloatTensor([
    total_samples / (num_classes * label_counts[i]) for i in range(num_classes)
])

print(f"Splits: Train={len(X_train)}, Val={len(X_val)}, Test={len(X_test)}")
print(f"Class distribution in training: {dict(label_counts)}")
print(f"Class weights: {class_weights}")

# Load FastText embeddings
print("\nLoading FastText embeddings...")
word_vectors = api.load("fasttext-wiki-news-subwords-300")
print("FastText loaded!")

# Text preprocessing
def preprocess_text(text):
    text = text.lower()
    text = re.sub(r'[^\w\s]', '', text)
    return text.split()

# Convert sentences to embeddings
def sentence_to_embedding(sentence, word_vectors):
    tokens = preprocess_text(sentence)
    embeddings = []
    
    for token in tokens:
        if token in word_vectors:
            embeddings.append(word_vectors[token])
    
    if embeddings:
        return np.mean(embeddings, axis=0)
    else:
        return np.zeros(300)

# Create embeddings for all data
print("\nCreating sentence embeddings...")
X_train_emb = np.array([sentence_to_embedding(sent, word_vectors) for sent in tqdm(X_train)])
X_val_emb = np.array([sentence_to_embedding(sent, word_vectors) for sent in tqdm(X_val)])
X_test_emb = np.array([sentence_to_embedding(sent, word_vectors) for sent in tqdm(X_test)])

# Convert to tensors
X_train_tensor = torch.FloatTensor(X_train_emb)
X_val_tensor = torch.FloatTensor(X_val_emb)
X_test_tensor = torch.FloatTensor(X_test_emb)
y_train_tensor = torch.LongTensor(y_train)
y_val_tensor = torch.LongTensor(y_val)
y_test_tensor = torch.LongTensor(y_test)



# MLP Model
class SentimentMLP(nn.Module):
    def __init__(self, input_dim=300, hidden_dim=128, num_classes=3, dropout_rate=0.3):
        super(SentimentMLP, self).__init__()
        
        self.network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
    def forward(self, x):
        return self.network(x)



# Training setup
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = SentimentMLP().to(device)
criterion = nn.CrossEntropyLoss(weight=class_weights.to(device))
optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='max', factor=0.5, patience=5, verbose=True)

# Move data to device
X_train_tensor = X_train_tensor.to(device)
X_val_tensor = X_val_tensor.to(device)
X_test_tensor = X_test_tensor.to(device)
y_train_tensor = y_train_tensor.to(device)
y_val_tensor = y_val_tensor.to(device)
y_test_tensor = y_test_tensor.to(device)



# Training function
def train_epoch(model, X, y, criterion, optimizer):
    model.train()
    
    batch_size = 64
    total_loss = 0
    all_preds = []
    all_labels = []
    
    indices = torch.randperm(len(X))
    
    for i in range(0, len(X), batch_size):
        batch_indices = indices[i:i+batch_size]
        batch_X = X[batch_indices]
        batch_y = y[batch_indices]
        
        optimizer.zero_grad()
        outputs = model(batch_X)
        loss = criterion(outputs, batch_y)
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
        _, predicted = torch.max(outputs, 1)
        all_preds.extend(predicted.cpu().numpy())
        all_labels.extend(batch_y.cpu().numpy())
    
    avg_loss = total_loss / (len(X) / batch_size)
    accuracy = np.mean(np.array(all_preds) == np.array(all_labels))
    macro_f1 = f1_score(all_labels, all_preds, average='macro')
    
    return avg_loss, accuracy, macro_f1

# Evaluation function
def evaluate(model, X, y, criterion):
    model.eval()
    
    with torch.no_grad():
        outputs = model(X)
        loss = criterion(outputs, y).item()
        _, predicted = torch.max(outputs, 1)
        
        accuracy = (predicted == y).float().mean().item()
        macro_f1 = f1_score(y.cpu(), predicted.cpu(), average='macro')
        
    return loss, accuracy, macro_f1, predicted



# Training loop
print("\nStarting training...")
num_epochs = 50
best_val_f1 = 0
patience_counter = 0
early_stop_patience = 15

train_losses, val_losses = [], []
train_accs, val_accs = [], []
train_f1s, val_f1s = [], []

for epoch in range(num_epochs):
    train_loss, train_acc, train_f1 = train_epoch(model, X_train_tensor, y_train_tensor, criterion, optimizer)
    val_loss, val_acc, val_f1, _ = evaluate(model, X_val_tensor, y_val_tensor, criterion)
    
    train_losses.append(train_loss)
    val_losses.append(val_loss)
    train_accs.append(train_acc)
    val_accs.append(val_acc)
    train_f1s.append(train_f1)
    val_f1s.append(val_f1)
    
    scheduler.step(val_f1)
    
    print(f"Epoch {epoch+1}/{num_epochs}")
    print(f"Train - Loss: {train_loss:.4f}, Acc: {train_acc:.4f}, F1: {train_f1:.4f}")
    print(f"Val   - Loss: {val_loss:.4f}, Acc: {val_acc:.4f}, F1: {val_f1:.4f}")
    print("-" * 50)
    
    if val_f1 > best_val_f1:
        best_val_f1 = val_f1
        torch.save(model.state_dict(), 'best_mlp_model.pth')
        patience_counter = 0
    else:
        patience_counter += 1
    
    if patience_counter >= early_stop_patience:
        print(f"Early stopping at epoch {epoch+1}")
        break

# Load best model
model.load_state_dict(torch.load('best_mlp_model.pth'))

# Plot training curves
fig, axes = plt.subplots(1, 3, figsize=(15, 5))

axes[0].plot(train_losses, label='Train')
axes[0].plot(val_losses, label='Validation')
axes[0].set_title('Loss vs Epochs')
axes[0].set_xlabel('Epochs')
axes[0].set_ylabel('Loss')
axes[0].legend()
axes[0].grid(True)

axes[1].plot(train_accs, label='Train')
axes[1].plot(val_accs, label='Validation')
axes[1].set_title('Accuracy vs Epochs')
axes[1].set_xlabel('Epochs')
axes[1].set_ylabel('Accuracy')
axes[1].legend()
axes[1].grid(True)

axes[2].plot(train_f1s, label='Train')
axes[2].plot(val_f1s, label='Validation')
axes[2].set_title('Macro F1 Score vs Epochs')
axes[2].set_xlabel('Epochs')
axes[2].set_ylabel('Macro F1')
axes[2].legend()
axes[2].grid(True)

plt.tight_layout()
plt.show()

# Test evaluation
test_loss, test_acc, test_f1, test_preds = evaluate(model, X_test_tensor, y_test_tensor, criterion)

print("\n=== TEST SET RESULTS ===")
print(f"Test Loss: {test_loss:.4f}")
print(f"Test Accuracy: {test_acc:.4f}")
print(f"Test Macro F1: {test_f1:.4f}")

# Classification report
print("\n=== CLASSIFICATION REPORT ===")
target_names = ['Negative', 'Neutral', 'Positive']
print(classification_report(y_test, test_preds.cpu().numpy(), target_names=target_names))

# Confusion matrix
cm = confusion_matrix(y_test, test_preds.cpu().numpy())
plt.figure(figsize=(8, 6))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=target_names, yticklabels=target_names)
plt.title('Confusion Matrix')
plt.ylabel('True Label')
plt.xlabel('Predicted Label')
plt.show()

# Final check
if test_f1 >= 0.65:
    print(f"\n✅ SUCCESS: Test Macro F1 = {test_f1:.4f} (Required: ≥ 0.65)")
else:
    print(f"\n❌ FAILED: Test Macro F1 = {test_f1:.4f} (Required: ≥ 0.65)")

# Model analysis
print("\n=== MODEL ANALYSIS ===")
print(f"Total parameters: {sum(p.numel() for p in model.parameters())}")
print(f"Best validation F1: {best_val_f1:.4f}")
print(f"Training stopped at epoch: {len(train_losses)}")

# Per-class performance
for i, class_name in enumerate(target_names):
    class_mask = (y_test == i)
    class_correct = (test_preds.cpu().numpy()[class_mask] == i).sum()
    class_total = class_mask.sum()
    class_acc = class_correct / class_total if class_total > 0 else 0
    print(f"{class_name} accuracy: {class_acc:.3f} ({class_correct}/{class_total})")