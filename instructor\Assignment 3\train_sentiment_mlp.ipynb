
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import gensim.downloader as api
from sklearn.metrics import f1_score, confusion_matrix, classification_report
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import re

# Set random seeds
torch.manual_seed(42)
np.random.seed(42)

# === DATA SETUP (from previous setup) ===
from datasets import load_dataset
from sklearn.model_selection import train_test_split

dataset = load_dataset('financial_phrasebank', 'sentences_50agree', trust_remote_code=True)
sentences = dataset['train']['sentence']
labels = dataset['train']['label']

X = np.array(sentences)
y = np.array(labels)

X_trainval, X_test, y_trainval, y_test = train_test_split(
    X, y, test_size=0.15, stratify=y, random_state=42
)
X_train, X_val, y_train, y_val = train_test_split(
    X_trainval, y_trainval, test_size=0.15, stratify=y_trainval, random_state=42
)

label_counts = Counter(labels)
total_samples = len(labels)
num_classes = 3
class_weights = torch.FloatTensor([
    total_samples / (num_classes * label_counts[i]) for i in range(num_classes)
])

print(f"Splits: Train={len(X_train)}, Val={len(X_val)}, Test={len(X_test)}")
print(f"Class weights: {class_weights}")





# === LOAD FASTTEXT EMBEDDINGS ===
print("Loading FastText embeddings...")
fasttext_model = api.load('fasttext-wiki-news-subwords-300')
print("FastText loaded!")

def preprocess_text(text):
    text = text.lower()
    text = re.sub(r'[^\w\s]', '', text)
    return text.split()

def get_sentence_embedding(sentence, model, embedding_dim=300):
    tokens = preprocess_text(sentence)
    vectors = []
    
    for token in tokens:
        if token in model:
            vectors.append(model[token])
    
    if vectors:
        return np.mean(vectors, axis=0)
    else:
        return np.zeros(embedding_dim)



# === CREATE SENTENCE EMBEDDINGS ===
print("Creating sentence embeddings...")

def create_embeddings(sentences):
    embeddings = []
    for sentence in sentences:
        emb = get_sentence_embedding(sentence, fasttext_model)
        embeddings.append(emb)
    return np.array(embeddings)

X_train_emb = create_embeddings(X_train)
X_val_emb = create_embeddings(X_val)
X_test_emb = create_embeddings(X_test)

print(f"Embedding shapes: Train={X_train_emb.shape}, Val={X_val_emb.shape}, Test={X_test_emb.shape}")



# === MLP MODEL ===
class SentimentMLP(nn.Module):
    def __init__(self, input_dim=300, hidden_dims=[256, 128], num_classes=3, dropout=0.5):
        super(SentimentMLP, self).__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout)
            ])
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, num_classes))
        self.model = nn.Sequential(*layers)
    
    def forward(self, x):
        return self.model(x)



# === TRAINING SETUP ===
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

model = SentimentMLP().to(device)
criterion = nn.CrossEntropyLoss(weight=class_weights.to(device))
optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)

# Convert to tensors
X_train_tensor = torch.FloatTensor(X_train_emb).to(device)
y_train_tensor = torch.LongTensor(y_train).to(device)
X_val_tensor = torch.FloatTensor(X_val_emb).to(device)
y_val_tensor = torch.LongTensor(y_val).to(device)

# === TRAINING FUNCTIONS ===
def evaluate_model(model, X, y):
    model.eval()
    with torch.no_grad():
        outputs = model(X)
        _, predicted = torch.max(outputs, 1)
        
        loss = criterion(outputs, y).item()
        accuracy = (predicted == y).float().mean().item()
        
        y_true = y.cpu().numpy()
        y_pred = predicted.cpu().numpy()
        macro_f1 = f1_score(y_true, y_pred, average='macro')
        
    return loss, accuracy, macro_f1

def train_epoch(model, X, y, optimizer):
    model.train()
    
    outputs = model(X)
    loss = criterion(outputs, y)
    
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()
    
    with torch.no_grad():
        _, predicted = torch.max(outputs, 1)
        accuracy = (predicted == y).float().mean().item()
        
        y_true = y.cpu().numpy()
        y_pred = predicted.cpu().numpy()
        macro_f1 = f1_score(y_true, y_pred, average='macro')
    
    return loss.item(), accuracy, macro_f1



# === TRAINING LOOP ===
num_epochs = 50
train_losses, val_losses = [], []
train_accs, val_accs = [], []
train_f1s, val_f1s = [], []

best_val_f1 = 0
best_model_state = None

print("Starting training...")

for epoch in range(num_epochs):
    train_loss, train_acc, train_f1 = train_epoch(model, X_train_tensor, y_train_tensor, optimizer)
    val_loss, val_acc, val_f1 = evaluate_model(model, X_val_tensor, y_val_tensor)
    
    scheduler.step(val_loss)
    
    train_losses.append(train_loss)
    val_losses.append(val_loss)
    train_accs.append(train_acc)
    val_accs.append(val_acc)
    train_f1s.append(train_f1)
    val_f1s.append(val_f1)
    
    if val_f1 > best_val_f1:
        best_val_f1 = val_f1
        best_model_state = model.state_dict().copy()
    
    if (epoch + 1) % 10 == 0:
        print(f'Epoch [{epoch+1}/{num_epochs}] - '
              f'Train Loss: {train_loss:.4f}, Train F1: {train_f1:.4f} | '
              f'Val Loss: {val_loss:.4f}, Val F1: {val_f1:.4f}')

print(f"\nBest validation F1: {best_val_f1:.4f}")

# === SAVE BEST MODEL ===
torch.save(best_model_state, 'best_mlp_model.pth')
print("Best model saved!")



# === PLOT TRAINING CURVES ===
epochs = range(1, num_epochs + 1)

fig, axes = plt.subplots(1, 3, figsize=(15, 4))

axes[0].plot(epochs, train_losses, 'b-', label='Train')
axes[0].plot(epochs, val_losses, 'r-', label='Validation')
axes[0].set_title('Loss vs Epochs')
axes[0].set_xlabel('Epochs')
axes[0].set_ylabel('Loss')
axes[0].legend()
axes[0].grid(True)

axes[1].plot(epochs, train_accs, 'b-', label='Train')
axes[1].plot(epochs, val_accs, 'r-', label='Validation')
axes[1].set_title('Accuracy vs Epochs')
axes[1].set_xlabel('Epochs')
axes[1].set_ylabel('Accuracy')
axes[1].legend()
axes[1].grid(True)

axes[2].plot(epochs, train_f1s, 'b-', label='Train')
axes[2].plot(epochs, val_f1s, 'r-', label='Validation')
axes[2].set_title('Macro F1 Score vs Epochs')
axes[2].set_xlabel('Epochs')
axes[2].set_ylabel('Macro F1 Score')
axes[2].legend()
axes[2].grid(True)

plt.tight_layout()
plt.show()



# === TEST EVALUATION ===
model.load_state_dict(best_model_state)
X_test_tensor = torch.FloatTensor(X_test_emb).to(device)
y_test_tensor = torch.LongTensor(y_test).to(device)

test_loss, test_acc, test_f1 = evaluate_model(model, X_test_tensor, y_test_tensor)

print(f"\n=== FINAL TEST RESULTS ===")
print(f"Test Loss: {test_loss:.4f}")
print(f"Test Accuracy: {test_acc:.4f}")
print(f"Test Macro F1: {test_f1:.4f}")



# === CONFUSION MATRIX ===
model.eval()
with torch.no_grad():
    outputs = model(X_test_tensor)
    _, predicted = torch.max(outputs, 1)

y_test_np = y_test_tensor.cpu().numpy()
y_pred_np = predicted.cpu().numpy()

cm = confusion_matrix(y_test_np, y_pred_np)
class_names = ['Negative', 'Neutral', 'Positive']

plt.figure(figsize=(8, 6))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
            xticklabels=class_names, yticklabels=class_names)
plt.title('Confusion Matrix - Test Set')
plt.ylabel('True Label')
plt.xlabel('Predicted Label')
plt.show()

print("\n=== CLASSIFICATION REPORT ===")
print(classification_report(y_test_np, y_pred_np, target_names=class_names))

success = "✅ SUCCESS" if test_f1 >= 0.65 else "❌ FAILED"
print(f"\n{success}: Test Macro F1 = {test_f1:.4f} (Required: ≥ 0.65)")