{"cells": [{"cell_type": "code", "execution_count": null, "id": "028a487a", "metadata": {}, "outputs": [], "source": ["---\n", "title: \"Assignment 2: Implement and Compare Word Embeddings\"\n", "author: \"<PERSON><PERSON>\"\n", "format: \n", "  html:\n", "    toc: true\n", "    toc-title: Contents\n", "    toc-depth: 4\n", "    self-contained: true\n", "    number-sections: false\n", "jupyter: python3\n", "---"]}, {"cell_type": "code", "execution_count": null, "id": "1a811094", "metadata": {"vscode": {"languageId": "markdown"}}, "outputs": [], "source": []}, {"cell_type": "raw", "id": "dca672e1", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "stat359-su25-py3.12", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}